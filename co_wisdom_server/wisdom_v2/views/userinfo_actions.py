from wisdom_v2.models import User, ProjectMember, Project, UserBackend, Permission
from rest_framework import serializers
from .company_actions import CompanySerializers
from wisdom_v2.views.role_action import RoleListSerializer
from .constant import ROLE_PROJECT_ADMIN
from utils.permission import get_permission


class UserInfoSerializer(serializers.ModelSerializer):
    birthday = serializers.DateField(format='%Y-%m-%d', read_only=True)
    roles = serializers.SerializerMethodField(read_only=True)
    company = serializers.SerializerMethodField(read_only=True)
    user_permission = serializers.SerializerMethodField(read_only=True)
    user_roles = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = User
        exclude = ('created_at', 'updated_at')
        extra_kwargs = {'password': {'write_only': True}}

    def get_user_permission(self, obj):
        user_backend = UserBackend.objects.filter(user=obj, deleted=False).first()
        if not user_backend:
            return []
        else:
            role = user_backend.role
            permission = Permission.objects.filter(permission_2_role__deleted=False, permission_2_role__role=role,
                                                   deleted=False)
            permissions = get_permission(permission)
            return permissions

    def get_user_roles(self, obj):
        user_backend = UserBackend.objects.filter(user=obj, deleted=False).first()
        if not user_backend:
            return {}
        else:
            role = user_backend.role
            return RoleListSerializer(role).data

    def get_roles(self, obj):
        project_member = ProjectMember.objects.filter(user=obj, deleted=False)
        # manager = Project.objects.filter(manager=obj)
        manager = Project.objects.filter(project_manager__user=obj, deleted=False)
        data_list = []
        for member in project_member:
            data_list.append({'user_id': member.user_id, 'project_id': member.project_id, 'role_id': member.role})
        for item in manager:
            data_list.append({'user_id': obj.pk, 'project_id': item.pk, 'role_id': ROLE_PROJECT_ADMIN})
        return data_list

    def get_company(self, obj):
        user_backend = UserBackend.objects.filter(user=obj, deleted=False, role__name='企业管理员').first()
        if user_backend:
            if user_backend.project:
                return CompanySerializers(user_backend.project.company).data
            if user_backend.company:
                return CompanySerializers(user_backend.company).data
        return

    def create(self, validated_data):
        instance = User(**validated_data)
        instance.save()
        return instance
