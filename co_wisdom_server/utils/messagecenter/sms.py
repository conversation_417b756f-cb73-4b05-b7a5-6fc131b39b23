#!/usr/bin/env python
# coding=utf-8
import logging
import requests
from django.conf import settings

from utils.message.lark_message import LarkMessageCenter
from wisdom_v2.models_file.platform_notification import PlatformNotification
from wisdom_v2.enum.service_content_enum import NoticeChannelTypeEnum

_logger = logging.getLogger('sms')


class SMS():
    appid = '65719'
    appkey = '2a980c003247b807685ffb29b62e7f86'

    def sendMsg(self, mobile, message, project_id=None, sender_id=None, receiver_id=None, subject='短信通知'):
        url = 'https://api.mysubmail.com/message/send.json'
        content = f'{settings.SMS_TEMPLATE_PREFIX}' + message
        data = {
            'appid': self.appid,
            'content': content,
            'signature': self.appkey,
            'to': mobile
        }
        res = requests.post(url, data)
        try:
            PlatformNotification.create_notification(
                subject=subject,
                template_name='sms',
                channel=NoticeChannelTypeEnum.sms.value,
                project_id=project_id,
                sender_id=sender_id,
                receiver_id=receiver_id,
                receiver_contact=mobile,
                status=1 if res.json().get('status') == 'success' else 2,
            )
            if res.json().get('status') != 'success':
                LarkMessageCenter().send_other_backend_message(
                    '短信发送失败', 'error',
                    f"mobile: {mobile}, message: {message}, res:{res.content}")
            return res.json()
        except Exception as e:
            LarkMessageCenter().send_other_backend_message(
                '短信发送失败', 'error',
                f"mobile: {mobile}, message: {message}, res:{res.content}，error: {str(e)}")


class SendSMS(object):

    def __init__(self):
        self.send_sms = SMS()


    def send_stakeholder_interview_reservation(self, coachee_name, phone, url, project_id=None, sender_id=None, receiver_id=None):
        text = f"""您的同事{coachee_name}邀请你为他提供发展反馈和建议，点击 {url} 确认访谈时间。"""
        return self.send_sms.sendMsg(phone, text, project_id, sender_id, receiver_id, '利益相关者访谈邀请')


    def send_update_interview_time_message(self, coach_name, before_time, now_time, url, phone, project_id=None, sender_id=None, receiver_id=None):
        text = f"您与{coach_name}教练的访谈会议从{before_time} 调整为 {now_time}，请您知晓。" \
               f"本次访谈将通过线上视频完成，请让您的手机处于良好的网络环境中，以获得更好的通话质量。点击 {url} 查看访谈详情。"
        return self.send_sms.sendMsg(phone, text, project_id, sender_id, receiver_id, '访谈时间变更通知')


    def send_stakeholder_create_interview(self, coach_name, interview_time, url, phone, project_id=None, sender_id=None, receiver_id=None):
        text = f"""你已成功确认{coach_name}教练的访谈时间为{interview_time}。点击 {url} 进入小程序，查看访谈详情，并可在预约时间进入小程序，通过视频对话完成访谈。"""
        return self.send_sms.sendMsg(phone, text, project_id, sender_id, receiver_id, '访谈确认通知')
    

    def send_stakeholder_interview_start_24h_notice(self, coach_name, coachee_name, interview_time, url, phone, project_id=None, sender_id=None, receiver_id=None):
        text = f"""{coach_name}教练对你的访谈将在明天{interview_time}进行，请提前安排工作日程，以便准时参加。本次访谈需要您提供对同事{coachee_name}的反馈，点击 {url} 查看访谈详情及提示，可以帮助您更好地进行准备。"""
        return self.send_sms.sendMsg(phone, text, project_id, sender_id, receiver_id, '访谈24小时提醒')


    def send_stakeholder_interview_start_30m_notice(self, coach_name, coachee_name, url, phone, project_id=None, sender_id=None, receiver_id=None):
        text = f"""距离{coach_name}教练和您的访谈会议还有30分钟，您可以提前回顾下您和同事{coachee_name}的工作过程，为访谈做好准备。本次访谈将通过线上视频完成，请让您的手机处于良好的网络环境中，以获得更好的通话质量点击 {url} 进入访谈。"""
        return self.send_sms.sendMsg(phone, text, project_id, sender_id, receiver_id, '访谈30分钟提醒')


    def send_coach_agree_interview_notice(self, coach_name, interview_time, url, phone, project_id=None, sender_id=None, receiver_id=None):
        text = f"""您预约的 {coach_name} 教练{interview_time}的一对一辅导教练已确认，您可以点击 {url} 进入小程序查看"""
        return self.send_sms.sendMsg(phone, text, project_id, sender_id, receiver_id, '教练确认通知')


    def send_update_interview_time_notice(self, coach_name, interview_time, url, phone, project_id=None, sender_id=None, receiver_id=None):
        text = f"""您预约的 {coach_name} 教练一对一辅导时间已修改为{interview_time}，您可以点击 {url} 进入小程序查看"""
        return self.send_sms.sendMsg(phone, text, project_id, sender_id, receiver_id, '辅导时间变更通知')


    def send_interview_start_30m_notice(self, coach_name, url, phone, project_id=None, sender_id=None, receiver_id=None):
        text = f"""您预约的 {coach_name} 教练的一对一辅导将于30分钟后开始，您可以点击 {url} 进入小程序参与教练辅导"""
        return self.send_sms.sendMsg(phone, text, project_id, sender_id, receiver_id, '辅导30分钟提醒')




