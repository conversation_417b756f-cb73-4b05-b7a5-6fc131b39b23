import datetime
import json
import math
import random
import pickle
import redis
from django.db import transaction

from django.db.models import When, Case
from decimal import Decimal, ROUND_UP
from django.conf import settings
from django.db.models import Sum, Q, Count
from pypinyin import lazy_pinyin


from utils.feishu_robot import send_lark_message
from utils.message.lark_message import LarkMessageCenter
from utils import task, validate
from wisdom_v2.app_views import work_wechat
from wisdom_v2.enum.business_order_enum import BusinessOrderSettlementStatusEnum, BusinessOrderPayStatusEnum, \
    BusinessOrderWithdrawalStatusEnum
from wisdom_v2.enum.pay_enum import OrderStatusEnum
from wisdom_v2.enum.project_enum import ProjectStatusEnum
from wisdom_v2.enum.resume_enum import ResumeCoachCourseTypeEnum
from wisdom_v2.models import PersonalApply, CompanyMember, ProjectMember, User, WorkWechatUser, Order
from wisdom_v2.enum.service_content_enum import UserInviteTypeEnum, CoachAuthEnum, ActivityTypeEnum, \
    ScheduleApplyTypeEnum, TagObjectTypeEnum
from wisdom_v2.enum.user_enum import CoachUserType<PERSON>num, PersonalApplyTypeEnum, CoachInternToPersonalStatus, \
    PersonalApplyStatusEnum, UserRoleEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewTypeEnum, ProjectInterviewPlaceCategoryEnum
from wisdom_v2.models import PersonalUser, ArticleThemeRelation, Coach, PublicAttr
from wisdom_v2.common import schedule_public, interview_public
from wisdom_v2.models import ProjectCoach, Resume, ProjectInterview
from wisdom_v2.models_file import BusinessOrder, CoachToClientNotes, TagObject
from wisdom_v2.views import constant
from wisdom_v2.views.constant import INTERVIEW_TYPE_COACHING, SCHEDULE_INTERVAL_MINUTES, ATTR_STATUS_INTERVIEW_CANCEL, \
    ORDER_TAX_POINT, PLATFORM_SERVICE_SCALE, NON_PLATFORM_SERVICE_SCALE, NON_ORDER_TAX_POINT, GENDER_TYPE

data_redis = redis.Redis.from_url(settings.DATA_REDIS)
third_party_redis = redis.Redis.from_url(settings.THIRD_PARTY_DATA_REDIS)


def get_project_coach_resume(user_id, project_id):
    """
    此函数用于获取项目教练的简历信息。
    1. 根据给定的条件查询项目教练。
    2. 如果找到符合条件的项目教练，并且他们的简历存在，那么获取并返回最新的定制化简历。
    3. 如果没有找到符合条件的项目教练，或者他们的简历不存在，那么获取并返回默认的简历。
    4. 如果在执行过程中遇到任何异常，捕获异常并通过LarkMessageCenter发送后端消息，然后返回错误信息。

    :param user_id: 教练的用户ID。
    :param project_id: 项目ID。

    :return: 如果成功，返回 (True, Resume对象)，如果失败，返回 (False, 错误信息)。
    """

    if not user_id or not project_id:
        return False, f'缺少关键参数信息'

    if not str(user_id).isdigit() or not str(project_id).isdigit():
        return False, f'关键参数格式错误'

    try:
        redis_key = f'{user_id}_{project_id}_resume'
        coach_resume_redis_data = data_redis.get(redis_key)
        if coach_resume_redis_data:
            resume = Resume.objects.get(pk=coach_resume_redis_data.decode(), deleted=False)
            data_redis.set(redis_key, resume.id, ex=10)
            return True, resume

        project_coach = ProjectCoach.objects.filter(
            coach__user_id=user_id,
            project_id=project_id,
            member__isnull=True,
            project_group_coach__isnull=True,
            resume__isnull=False,
            deleted=False
        ).first()
        # 如果教练加入项目，则去看定制化简历
        if project_coach and project_coach.show_resume:
            return True, project_coach.show_resume
        # 如果没有，则返回主简历
        resume = Resume.objects.get(coach__user_id=user_id, is_customization=False, deleted=False)
        data_redis.set(redis_key, resume.id, ex=10)
        return True, resume
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'get_project_coach_resume', 'error',
            {'msg:': str(e), 'params': f'user_id:{user_id}, project_id:{project_id}'}
        )
        return False, '获取简历信息失败'


def get_coach_to_c_interview_count(coach_user_id):
    """
    获取教练C端辅导次数
    :param coach_user_id: 教练用户的ID  type: int
    :return: 辅导次数  type: int
    """

    interview = ProjectInterview.objects.filter(
        deleted=False,
        public_attr__user_id=coach_user_id,
        type=INTERVIEW_TYPE_COACHING,
        public_attr__project__isnull=True).exclude(
        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
    return interview.count()


def is_coachee_invitation_from_a_coach(coach_user_id, coachee_user_id):
    """
    检查个人客户是否是通过教练分享的教练简历邀请进来的。

    :param coach_user_id: 教练用户的ID  type: int
    :param coachee_user_id: 个人客户的ID  type: int
    :return: 是否是通过教练邀请进来的个人客户  type: bool
    """

    # 如果没有必传参数返回False
    if not all([coach_user_id, coachee_user_id]):
        return False

    # 参数类型限制int
    if not all([isinstance(coach_user_id, int), isinstance(coachee_user_id, int)]):
        return False

    # 是通过教练邀请进来的个人客户， 并且邀请创建人是该教练。
    personal_user = PersonalUser.objects.filter(
        user_id=coachee_user_id, invite__referrer_id=coach_user_id,
        invite__type__in=UserInviteTypeEnum.source_coach_invitation(),
        deleted=False)

    # 如果personal_user为空，表示不满足条件，返回False；否则返回True
    if personal_user.exclude():
        return True
    return False


def calculate_coach_income_details(interview, tax_point=ORDER_TAX_POINT):
    """
    计算教练的订单收入细节，包括平台服务费占比、税后实际收入和其他相关财务细节。
    默认所有辅导税率都是 3%

    :param order: 订单对象，type: Order
    :param coach_user_id: 教练用户的ID，type: int
    :param coachee_user_id: 客户用户的ID，type: int
    :param tax_point: 税率，默认从 ORDER_TAX_POINT 获取
    :return: 平台服务费百分比，税后实际收入，税额，平台服务费金额
    """
    order = interview.order
    coach_user_id = interview.public_attr.user_id
    coachee_user_id = interview.public_attr.target_user_id

    # 检查客户是否是通过教练的邀请注册的
    is_coach_invite = is_coachee_invitation_from_a_coach(coach_user_id, coachee_user_id)

    # 计算订单中单个辅导的金额
    single_payer_amount = Decimal(order.payer_amount) / order.count
    # 根据来源，计算不同的服务费
    real_tax_point = tax_point

    # 如果是公益活动，无抽成
    if order.activity and order.activity.type == ActivityTypeEnum.public_welfare_coach.value:
        platform_service_scale = NON_PLATFORM_SERVICE_SCALE
        
    else:
        # 如果是个人海报邀请来的，先看有没有设置特殊平台费用抽成
        # 如果没有设置，则看教练是否设置了
        # 如果教练没有设置，则看是否是通过教练邀请进来的
        # 如果不是，则按默认比例计算
        if order.personal_activity and order.personal_activity.platform_fee_rate is not None:
            platform_service_scale = order.personal_activity.platform_fee_rate
        else:
            coach = Coach.objects.filter(user_id=coach_user_id, deleted=False).first()
            if coach and coach.platform_fee_rate is not None:
                platform_service_scale = coach.platform_fee_rate
                real_tax_point = NON_ORDER_TAX_POINT
            else:
                if is_coach_invite:
                    platform_service_scale = NON_PLATFORM_SERVICE_SCALE
                else:
                    platform_service_scale = PLATFORM_SERVICE_SCALE
    
    
    # 平台服务费计算
    platform_service_amount = math.floor(single_payer_amount * (Decimal(platform_service_scale) / Decimal('100')))
    # 税费计算
    tax_amount = math.floor(single_payer_amount * (Decimal(real_tax_point) / Decimal('100')))
    # 收入计算
    real_income = math.ceil(single_payer_amount - platform_service_amount - tax_amount)
    return platform_service_scale, int(real_income), tax_amount, platform_service_amount, real_tax_point


def calculate_coach_order_income_details(order, coach_user_id, coachee_user_id):
    """
    计算教练的整笔订单收入，包括平台服务费占比、税后实际收入和其他相关财务细节。

    :param order: 订单对象，type: Order
    :param coach_user_id: 教练用户的ID，type: int
    :param coachee_user_id: 客户用户的ID，type: int
    :return: 平台服务费百分比，税后实际收入，税额，平台服务费金额
    """

    # 检查客户是否是通过教练的邀请注册的
    is_coach_invite = is_coachee_invitation_from_a_coach(coach_user_id, coachee_user_id)

    # 计算订单中单个辅导的金额
    single_payer_amount = Decimal(order.payer_amount)
    # 根据来源，计算不同的服务费

    # 如果是公益活动，无抽成
    if order.activity and order.activity.type == ActivityTypeEnum.public_welfare_coach.value:
        platform_service_scale = NON_PLATFORM_SERVICE_SCALE
        tax_point = ORDER_TAX_POINT
    else:
        tax_point = ORDER_TAX_POINT
        if order.personal_activity and order.personal_activity.platform_fee_rate is not None:
            platform_service_scale = order.personal_activity.platform_fee_rate
        else:
            coach = Coach.objects.filter(user_id=coach_user_id, deleted=False).first()
            if coach and coach.platform_fee_rate is not None:
                platform_service_scale = coach.platform_fee_rate
                tax_point = NON_ORDER_TAX_POINT
            else:
                if is_coach_invite:
                    platform_service_scale = NON_PLATFORM_SERVICE_SCALE
                else:
                    platform_service_scale = PLATFORM_SERVICE_SCALE

    # 平台服务费计算
    platform_service_amount = math.floor(single_payer_amount * (Decimal(platform_service_scale) / Decimal('100')))
    # 税费计算
    tax_amount = math.floor(single_payer_amount * (Decimal(tax_point) / Decimal('100')))
    # 收入计算
    real_income = math.ceil(single_payer_amount - platform_service_amount - tax_amount)
    return platform_service_scale, int(real_income), tax_amount, platform_service_amount



def get_coach_available_time_period(user_id, start_date, end_date, duration, apply_type=ScheduleApplyTypeEnum.project.value):
    """
    计算指定日期范围内教练的所有可用时间段和总可用小时数。

    :param user_id: 教练的用户ID type: int
    :param start_date: 需要查询的开始日期 type: datetime
    :param end_date: 需要查询的结束日期 type: datetime
    :param duration: 最小时间段长度，单位小时 type: float
    :param apply_type: 查询的日程应用类型，默认企业项目
    :return: 总可用小时数和包含所有可用时间段的字符串。时间段的格式为 "开始时间-结束时间" type: tuple
    """

    # 初始化存放所有可用时间段的字符串和总小时数
    day_time_msg = ''
    total_hours = 0

    # 从开始日期遍历到结束日期
    target_date = start_date
    for item in range((end_date - start_date).days + 1):
        # 将最小时间段长度从小时转换为分钟，并且考虑到时间间隔
        min_duration = int(duration * 60 / SCHEDULE_INTERVAL_MINUTES)

        # 获取教练在当前日期的时间段布尔列表
        time_slots = schedule_public.get_schedule_day_list(
            user_id, datetime.datetime.combine(target_date, datetime.time.min), apply_type=apply_type)

        # 计算当前日期的可用时间段和总小时数
        day_hours, periods = schedule_public.get_available_periods(time_slots, target_date, min_duration)

        # 将可用时间段添加到字符串中
        day_time_msg += (target_date.strftime('%Y.%m.%d ') + '，'.join(periods) + '\n')

        # 将总小时数添加到总计中
        total_hours += day_hours

        # 进入下一天
        target_date += datetime.timedelta(days=1)

    return int(total_hours), day_time_msg


def get_coach_income(coach):
    # 计算教练的已提现收入
    withdrawn_income = BusinessOrder.objects.filter(
        deleted=False,  # 筛选未删除的订单
        coach=coach,  # 筛选指定教练的订单
        settlement_status=BusinessOrderSettlementStatusEnum.settled,  # 筛选已结算的订单
        withdrawal_status=BusinessOrderWithdrawalStatusEnum.withdrawn.value  # 筛选已提现的订单
    ).aggregate(withdrawn_income=Sum('coach_actual_income'))  # 对教练实际收入进行求和

    # 如果查询结果中有已提现收入，则使用该值，否则设置为0
    withdrawn_income = withdrawn_income.get('withdrawn_income', 0) if withdrawn_income.get('withdrawn_income', 0) else 0
    # 将已提现收入从整数分转换为小数元
    withdrawn_income = Decimal(str(withdrawn_income)) / Decimal('100')

    # 计算教练的未提现收入
    not_withdrawn_income = BusinessOrder.objects.filter(
        deleted=False,  # 筛选未删除的订单
        coach=coach,  # 筛选指定教练的订单
        settlement_status=BusinessOrderSettlementStatusEnum.unsettled,  # 筛选未结算的订单
        withdrawal_status__in=[BusinessOrderWithdrawalStatusEnum.can_withdraw.value,
                               BusinessOrderWithdrawalStatusEnum.withdrawn.value],  # 筛选可提现和已提现状态的订单(已提现未结算的订单属于可提现订单)
        pay_status__in=[BusinessOrderPayStatusEnum.paid, BusinessOrderPayStatusEnum.non_payment]  # 筛选已支付和未支付无风险的订单
    ).aggregate(not_withdrawn_income=Sum('coach_actual_income'))  # 对教练实际收入进行求和

    # 如果查询结果中有未提现收入，则使用该值，否则设置为0
    not_withdrawn_income = not_withdrawn_income.get('not_withdrawn_income', 0) \
        if not_withdrawn_income.get('not_withdrawn_income', 0) else 0
    # 将未提现收入从整数分转换为小数元
    not_withdrawn_income = Decimal(str(not_withdrawn_income)) / Decimal('100')

    # 计算教练的累计收入（已提现和未提现收入之和）
    cumulative_income = not_withdrawn_income + withdrawn_income

    # 返回累计收入、已提现收入和未提现收入
    return cumulative_income, withdrawn_income, not_withdrawn_income



def appointment_filter_coach(coach_list, user):
    """
    筛选出预约中的教练
    :param coach_list:
    :param user:
    :return: appointment_filter_list 教练列表
    :return: appointment_filter_list 教练id列表

    """
    exists_coach_ids = []
    appointment_filter_list = []
    coach_user_ids = list(ProjectInterview.objects.filter(
        deleted=False,
        public_attr__target_user_id=user.id,
        public_attr__project__isnull=True,
        type=ProjectInterviewTypeEnum.formal_interview,
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one,
    ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL
              ).values_list('public_attr__user_id', flat=True))
    for coach in coach_list:
        if coach.user_id in coach_user_ids:
            appointment_filter_list.append(coach)
            exists_coach_ids.append(coach.id)
    return appointment_filter_list, exists_coach_ids


def select_theme_filter_coach(coach_list, exists_coach_ids, user):
    """
    筛选出搜索过的主题中的教练
    :param coach_list:
    :param exists_coach_ids:
    :param user:
    :return:
    """
    theme_filter_list, exists_theme_coach_ids = [], []
    if data_redis.get(f"theme__{user.id}"):
        theme_ids = pickle.loads(data_redis.get(f"theme__{user.id}"))
        theme_coach_ids = list(ArticleThemeRelation.objects.filter(
            theme_id__in=theme_ids, deleted=False, coach_id__isnull=False).values_list('coach_id', flat=True))
        for coach in coach_list:
            if coach.id in exists_coach_ids:
                continue
            if coach.id in theme_coach_ids:
                theme_filter_list.append(coach)
                exists_theme_coach_ids.append(coach.id)

    return theme_filter_list, exists_theme_coach_ids


def quick_link_filter_coach(coach_list, exists_coach_ids, user):
    """
    筛选出点击了金刚区的教练
    :param coach_list:
    :param exists_coach_ids:
    :param user:
    :return:
    """
    quick_link_filter_list, exists_quick_link_coach_ids = [], []
    if data_redis.get(f"quick_link__{user.id}"):
        theme_id = data_redis.get(f"quick_link__{user.id}").decode()
        theme_coach_ids = list(ArticleThemeRelation.objects.filter(
            theme_id=theme_id, deleted=False, coach_id__isnull=False).values_list('coach_id', flat=True))
        for coach in coach_list:
            if coach.id in exists_coach_ids:
                continue
            if coach.id in theme_coach_ids:
                quick_link_filter_list.append(coach)
                exists_quick_link_coach_ids.append(coach.id)

    return quick_link_filter_list, exists_quick_link_coach_ids


def resume_filter_coach(coach_list, exists_coach_ids, user):
    """
    筛选出点击了简历的教练
    :param coach_list:
    :param exists_coach_ids:
    :param user:
    :return:
    """
    resume_filter_list, exists_resume_coach_ids = [], []
    if data_redis.get(f'app_resume_detail_{user.id}'):
        resume_id = data_redis.get(f'app_resume_detail_{user.id}').decode()
        resume = Resume.objects.filter(id=resume_id, deleted=False).first()
        if resume:
            domain_list = resume.coach_domain
            if domain_list:
                temp = Q()
                temp.connector = 'OR'
                for item in domain_list:
                    temp.children.append(('coach_domain__contains', item))
                coach_ids = list(Resume.objects.filter(Q(temp), is_customization=False,
                                                       deleted=False).values_list('coach__id', flat=True))
                coach_ids = list(set(coach_ids))
                for coach in coach_list:
                    if coach.id in exists_coach_ids:
                        continue
                    if coach.id in coach_ids:
                        resume_filter_list.append(coach)
                        exists_resume_coach_ids.append(coach.id)

    return resume_filter_list, exists_resume_coach_ids


def coach_appointment_filter_coach(coach_list, exists_coach_ids):
    """
    14天内教练在平台上被预约的次数，次数多的排序在前
    """
    coach_appointment_filter_list, coach_appointment_filter_exists_coach_ids = [], []
    now = datetime.datetime.now()
    fourteen_days_ago = now - datetime.timedelta(days=14)

    result = PublicAttr.objects.filter(
        start_time__gte=fourteen_days_ago, start_time__lte=now, type=INTERVIEW_TYPE_COACHING,
        project__isnull=True).exclude(status=ATTR_STATUS_INTERVIEW_CANCEL).values('user').annotate(
        count=Count('user')).order_by('-count')

    if result:
        coach_ids_from_result = {item['user'] for item in result}
        user_ids = [coach.user_id for coach in coach_list if coach.id not in exists_coach_ids]

        filtered_coaches = []
        for user_id in coach_ids_from_result:
            coach = Coach.objects.filter(user_id=user_id, deleted=False).first()
            if coach and coach.id not in exists_coach_ids and user_id in user_ids:
                filtered_coaches.append(coach)

        coach_appointment_filter_list = filtered_coaches
        coach_appointment_filter_exists_coach_ids = [coach.id for coach in filtered_coaches]

    return coach_appointment_filter_list, coach_appointment_filter_exists_coach_ids


def coach_resume_count_filter(coach_list, total_coach_list):
    """
    14天内教练的简历被查看次数，次数多排序在前
    """
    total_coach_ids = [coach.id for coach in total_coach_list]
    all_coach_list = [coach for coach in coach_list if coach.id not in total_coach_ids]
    new_coach_list = []
    for coach in all_coach_list:
        count = len(data_redis.keys(f"app_resume_detail_count_{coach.id}*"))
        new_coach_list.append([coach, count])
    if new_coach_list:
        sorted_list = sorted(new_coach_list, key=lambda x: x[-1], reverse=True)
        coach_resume_count_filter_list = [item[0] for item in sorted_list]
    else:
        coach_resume_count_filter_list = all_coach_list
    return coach_resume_count_filter_list


def to_C_coach_list(coach_list, user):
    total_coach_list, exists_coach_ids = [], []
    if user:
        appointment_filter_list, appointment_filter_exists_coach_ids = appointment_filter_coach(coach_list, user)
        if appointment_filter_list:
            total_coach_list.extend(appointment_filter_list)
            exists_coach_ids.extend(appointment_filter_exists_coach_ids)

        select_theme_filter_list, select_theme_filter_exists_coach_ids = select_theme_filter_coach(coach_list, exists_coach_ids, user)
        if select_theme_filter_list:
            total_coach_list.extend(select_theme_filter_list)
            exists_coach_ids.extend(select_theme_filter_exists_coach_ids)

        quick_link_filter_list, quick_link_filter_exists_coach_ids = quick_link_filter_coach(coach_list, exists_coach_ids, user)
        if quick_link_filter_list:
            total_coach_list.extend(quick_link_filter_list)
            exists_coach_ids.extend(quick_link_filter_exists_coach_ids)

        resume_filter_list, resume_filter_exists_coach_ids = resume_filter_coach(coach_list, exists_coach_ids,user)
        if resume_filter_list:
            total_coach_list.extend(resume_filter_list)
            exists_coach_ids.extend(resume_filter_exists_coach_ids)

    coach_appointment_filter_list, coach_appointment_filter_exists_coach_ids = coach_appointment_filter_coach(coach_list, exists_coach_ids)
    if coach_appointment_filter_list:
        total_coach_list.extend(coach_appointment_filter_list)
        exists_coach_ids.extend(coach_appointment_filter_exists_coach_ids)

    if len(total_coach_list) == len(coach_list):
        return total_coach_list

    coach_resume_count_filter_list = coach_resume_count_filter(coach_list, total_coach_list)
    if coach_resume_count_filter_list:
        total_coach_list.extend(coach_resume_count_filter_list)

    return total_coach_list


# 获取符合条件的C端条教练列表
def get_bash_to_c_coach_list():
    coach = Coach.objects.filter(
        deleted=False, resumes__coach_experience__isnull=False,  # 已填写教练经验
        order_receiving_status=True,  # 用户愿意接单
        resumes__coach_auth__in=CoachAuthEnum.get_describe_keys(),  # 已填写教练认证
        platform_order_receiving_status=True,  # 平台允许接单
        resumes__is_customization=False,  # 只看主简历
        price__isnull=False,  # 设置辅导金额
        coach_type__in=CoachUserTypeEnum.personal_coach_value()  # 教练类型为个人教练
    )
    return coach


def get_to_c_search_coach_list():
    """
        C端搜索功能，返回的基础教练数据表
    """

    coach = Coach.objects.filter(
        deleted=False,
        order_receiving_status=True,  # 用户愿意接单
        platform_order_receiving_status=True,  # 平台允许接单
        resumes__is_customization=False,  # 只看主简历
        price__isnull=False,  # 设置辅导金额
        coach_type__in=CoachUserTypeEnum.trainee_coach_value()  # 教练类型为个人教练+见习教练
    )
    return coach


# 获取随机个人教练列表
def get_random_trainee_coach_list(order_by_coach_ids=None):
    # 查询最新个人教练列表
    coach = get_bash_to_c_coach_list().distinct()

    if order_by_coach_ids:
        # 如果有缓存key就按照缓存的查询
        coach_order_list = Case(*[When(pk=order, then=pos) for pos, order in enumerate(order_by_coach_ids)])
        coach = coach.order_by(coach_order_list)
    else:
        # 如果没有缓存记录则生成缓存记录id列表
        raw_coach_order_list = list(coach.values_list('id', flat=True))
        order_by_coach_ids = []
        count = len(raw_coach_order_list)
        for item in range(count):
            tmp_coach_id = random.choice(raw_coach_order_list)
            order_by_coach_ids.append(tmp_coach_id)
            raw_coach_order_list.remove(tmp_coach_id)
        # 按照随机生成的查询列表排序查询
        coach_order_list = Case(*[When(pk=order, then=pos) for pos, order in enumerate(order_by_coach_ids)])
        coach = coach.order_by(coach_order_list)

    return coach, order_by_coach_ids


def coach_resume_entry_update(data, empower_type, coach):
    """
    根据 empower_type 更新个人申请和个人简历
    :param data: 用户提交的数据字典
    :param empower_type: 申请类型
    :param coach: 教练实例
    :return: 创建的 PersonalApply 实例
    """
    resume = coach.resumes.filter(deleted=False, is_customization=False).first()  # 获取教练的第一个非定制简历实例
    coach_entry_update_user(data, coach.user)  # 更新用户个人信息
    coach_entry_update_data(data, coach)  # 更新教练个人信息
    coach_entry_update_resume(data, resume)  # 更新简历个人信息

    # 如果是申请成为个人教练。即时提醒
    if empower_type == PersonalApplyTypeEnum.personal.value:
        if 'price' in data.keys():
            # 创建新的见习申请转个人教练记录记录
            apply_instance = PersonalApply.objects.create(
                type=empower_type, true_name=coach.user.true_name, phone=coach.user.phone,
                email=coach.user.email, coach=coach, user_class=coach.user_class
            )
            coach.intern_to_personal_status = CoachInternToPersonalStatus.apply.value
            coach.save()
            send_lark_message.delay([apply_instance.pk], 'add_personal_apply', sender_id=coach.user.id)
        else:
            # 见习教练申请转个人教练时填写简历信息，设置价格
            coach.intern_to_personal_status = CoachInternToPersonalStatus.set_price.value
            coach.save()
            apply_instance = None
    else:
        apply_instance = PersonalApply.objects.create(  # 创建新的见习教练申请记录
            type=empower_type, true_name=coach.user.true_name, phone=coach.user.phone,
            email=coach.user.email, coach=coach, user_class=coach.user_class
        )

    return apply_instance


def coach_resume_entry_review(resume_data, empower_type, user_id=None):  # 检查个人申请和个人简历的必填参数
    """
    检查个人申请和个人简历的必填项
    :param resume_data: 用户提交的数据字典
    :param empower_type: 申请类型
    :param user_id: 用户id  用于验证邮箱/手机号是否出现重复 为空时不验证
    :return: 如果存在缺失的必填项，返回提示信息；否则，返回 None
    """
    if empower_type == PersonalApplyTypeEnum.personal.value:  # 如果是个人申请类型
        # 之前修改简历传language参数实际修改coach_language字段，这里需要再次处理转换
        # 用户填写是language 简历读取是coach_language， 可以使用or判断
        language = resume_data.get('language') or resume_data.get('coach_language')
        coach_auth = resume_data.get('coach_auth')
        coach_industry = resume_data.get('coach_industry')
        coach_domain = resume_data.get('coach_domain')
        if not all([language, coach_auth, coach_industry, coach_domain]):  # 如果任何一个必填项不存在，返回提示信息
            return "缺少必填参数"
    elif empower_type == PersonalApplyTypeEnum.internship.value:  # 如果是实习申请类型
        email = resume_data.get('email')
        phone = resume_data.get('phone')
        customer_evaluate = resume_data.get('customer_evaluate')
        coach_style = resume_data.get('coach_style')
        coach_experience = resume_data.get('coach_experience')
        work_experience = resume_data.get('work_experience')
        posters_text = resume_data.get('posters_text')
        head_image_url = resume_data.get('head_image_url')
        personal_name = resume_data.get('personal_name')
        job_profile = resume_data.get('job_profile')
        if not all([email, phone, customer_evaluate, coach_style, coach_experience, work_experience,
                    posters_text, head_image_url, personal_name]):  # 如果任何一个必填项不存在，返回提示信息
            return "缺少必填参数"
        
        # 新增加必填参数，小程序新旧版本兼容处理，此参数单独提示。
        if not job_profile:
            return '请填写"一句话介绍工作经历"'

        if not validate.validate_email(email):
            return '邮箱错误'
        if not validate.validate_phone(phone):
            return '手机号格式错误'

        if user_id:
            if User.objects.filter(email=email).exclude(id=user_id).exists():
                return '当前邮箱已存在'
            if User.objects.filter(phone=phone).exclude(id=user_id).exists():
                return '当前手机号已存在'
    else:
        return
    return


def coach_entry_update_data(validated_data, coach):
    """
    更新教练的信息
    :param validated_data: 验证过的数据字典
    :param coach: 教练实例
    :return: 更新后的数据字典
    """
    if 'personal_name' in validated_data.keys():
        coach.personal_name = validated_data['personal_name']

    if 'city' in validated_data.keys():
        coach.city = validated_data['city']

    if 'posters_text' in validated_data.keys():
        coach.posters_text = validated_data['posters_text']

    if 'order_receiving_status' in validated_data.keys():
        coach.order_receiving_status = validated_data['order_receiving_status']

    # 如果价格为 None，则更新为 Null，否则将价格转换为分并保留整数
    if 'price' in validated_data.keys():
        price = validated_data['price']
        coach.price = None if price is None else int(Decimal(str(price)) * Decimal('100').quantize(Decimal('0')))

    # 保存更新
    coach.save()

    # 返回更新后的数据字典
    return validated_data


def coach_entry_update_user(validated_data, user):
    """
    更新用户信息
    :param validated_data: 经过验证的数据字典
    :param user: 要更新的用户实例
    :return: 返回更新后的新数据字典
    """
    if 'email' in validated_data.keys():
        user.email = validated_data['email']  # 更新用户的邮箱地址
    if 'phone' in validated_data.keys():
        user.phone = validated_data['phone']  # 更新用户的电话号码
    if 'birthday' in validated_data.keys():
        user.birthday = validated_data['birthday']  # 更新用户的生日
    if 'english_name' in validated_data.keys():  # 更新用户的英文名
        user.english_name = validated_data['english_name']
    if 'gender' in validated_data.keys():  # 更新用户的性别
        user.gender = validated_data['gender']
    user.save()  # 保存更新
    return validated_data


def coach_entry_update_resume(validated_data, resume):
    """
    更新简历信息
    :param validated_data: 经过验证的数据字典
    :param resume: 要更新的简历实例
    :return: 返回更新后的新数据字典
    """
    if 'coach_auth' in validated_data.keys():
        resume.coach_auth = validated_data['coach_auth']  # 更新教练认证
    if 'coach_industry' in validated_data.keys():
        resume.coach_industry = validated_data['coach_industry']  # 更新行业经验
    if 'coach_domain' in validated_data.keys():
        resume.coach_domain = validated_data['coach_domain']  # 更新专业领域
    if 'qualification' in validated_data.keys():
        resume.qualification = validated_data['qualification']  # 更新资质证书
    if 'one_to_one_interview_time' in validated_data.keys():
        resume.one_to_one_interview_time = validated_data['one_to_one_interview_time']  # 更新一对一面试时间
    if 'language' in validated_data.keys():
        resume.coach_language = validated_data['language']  # 更新授课语言
    if 'customer_evaluate' in validated_data.keys():
        resume.customer_evaluate = validated_data['customer_evaluate']  # 更新客户评价
    if 'coach_style' in validated_data.keys():
        resume.coach_style = validated_data['coach_style']  # 更新教练风格
    if 'coach_experience' in validated_data.keys():
        resume.coach_experience = validated_data['coach_experience']  # 更新教练经验
    if 'work_experience' in validated_data.keys():
        resume.work_experience = validated_data['work_experience']  # 更新工作经历
    if 'job_profile' in validated_data.keys():
        resume.job_profile = validated_data['job_profile']  # 更新一句话介绍工作经历
    if 'working_years' in validated_data.keys():
        resume.working_years = validated_data['working_years']  # 更新工作经历
    if 'english_name' in validated_data.keys():  # 更新简历的英文名
        resume.english_name = validated_data['english_name']
    if 'head_image_url' in validated_data.keys():
        resume.head_image_url = validated_data['head_image_url']  # 更新头像
        task.update_coach_resume_share_url.delay(resume.pk, validated_data['head_image_url'])  # 更新小程序教练简历分享的头像
    resume.save()  # 保存更新
    return validated_data


def get_intern_apply_status(coach):
    """
    获取见习教练转个人教练的申请状态
    """

    status = None
    # 如果是见习教练
    if coach.coach_type == CoachUserTypeEnum.student.value:

        # 查询最新的入驻申请
        apply = PersonalApply.objects.filter(
            coach=coach, deleted=False).first()
        if apply:
            if apply.status == PersonalApplyStatusEnum.passed.value:
                status = 1  # 如果入驻申请通过，可以进入详情页
            else:
                status = 2  # 如果入驻申请没有通过，弹窗提示
        else:
            status = 2  # 如果没有入驻申请，弹窗提示

    # 如果是个人教练
    elif coach.coach_type in CoachUserTypeEnum.personal_coach_value():
        # 查询是否存在见习教练入驻申请（用于区分用户申请成为个人教练&见习教练申请成为个人教练）
        internship_apply = PersonalApply.objects.filter(
            coach=coach, deleted=False, type=PersonalApplyTypeEnum.internship.value).first()
        if internship_apply:
            # 查询见习教练转个人教练申请
            if PersonalApply.objects.filter(coach=coach, deleted=False, type=PersonalApplyTypeEnum.personal.value).exists():
                status = 1
    return status


def get_coach_course_to_image(coach_course):
    """
    获取受训经验对应的换为图片列表
    : pram coach_course: 受训经验中文描述列表
    : return: 图片url列表
    """

    if not isinstance(coach_course, list):
        coach_course = list(coach_course)

    image_dict = ResumeCoachCourseTypeEnum.get_image()

    coach_course_image = []
    for item in coach_course:
        if image_dict.get(item):
            coach_course_image.append(image_dict.get(item))
    return coach_course_image if coach_course_image else None


def get_coachee_company(user):
    company_member = CompanyMember.objects.filter(user=user).first()
    if company_member:
        company = company_member.company.real_name
        return company
    return


def get_coach_to_coachee_list(coach_user, user_role):
    """
    获取教练对应的学员列表
    :param coach_user: 教练用户
    :param user_role: 用户角色
    :return: 分类排序的学员列表
    """
    base_coach_interview = ProjectInterview.objects.filter(
        deleted=False,
        public_attr__user=coach_user.id, public_attr__type=constant.ATTR_TYPE_INTERVIEW,
        public_attr__target_user_id__isnull=False).exclude(
        order__status__in=[OrderStatusEnum.pending_pay, OrderStatusEnum.under_refund, OrderStatusEnum.refunded]
    ).exclude(type=ProjectInterviewTypeEnum.chemical_interview).exclude(
        type=ProjectInterviewTypeEnum.stakeholder_interview.value  # 客户列表来源排除利益相关者访谈的辅导
    )

    # 获取全部的个人辅导对应的用户
    interview_coachee_id_list = list(set(base_coach_interview.values_list('public_attr__target_user_id', flat=True)))

    interview_coachee_user_list = User.objects.filter(id__in=interview_coachee_id_list, deleted=False).all()

    interview_progress = []  # 个人进行中
    interview_not_start = []  # 个人未开始
    interview_completed = []  # 个人已结束

    for item in interview_coachee_user_list:

        # 查询辅导进行中的
        if base_coach_interview.filter(
            is_coach_agree=True, public_attr__start_time__lte=datetime.datetime.now(),
            public_attr__target_user_id=item.id, public_attr__end_time__gte = datetime.datetime.now()
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exists():
            interview_progress.append(coach_customer_to_dict(item, coach_user.id,  UserRoleEnum.trainee_coachee.value))

        # 查询辅导未同意的
        elif base_coach_interview.filter(public_attr__target_user_id=item.id, is_coach_agree=False).exclude(
                public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exists():
            interview_not_start.append(coach_customer_to_dict(item, coach_user.id,  UserRoleEnum.trainee_coachee.value))

        # 查询辅导未开始的
        elif base_coach_interview.filter(
                public_attr__target_user_id=item.id, is_coach_agree=True, public_attr__start_time__gt=datetime.datetime.now()
            ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exists():
            interview_not_start.append(coach_customer_to_dict(item, coach_user.id, UserRoleEnum.trainee_coachee.value))

        # 查询辅导已结束的/已取消的
        elif base_coach_interview.filter(Q(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL) | Q(
                ~Q(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL),
                is_coach_agree=True, public_attr__target_user_id=item.id,
                public_attr__end_time__lt=datetime.datetime.now())).exists():
            interview_completed.append(coach_customer_to_dict(item, coach_user.id,  UserRoleEnum.trainee_coachee.value))

    project_not_start = []  # 项目未开始
    project_progress = []  # 项目进行中
    project_completed = []  # 项目已结束

    project_coachee_id_list = []

    # 如果是项目教练，还需要获取项目匹配的客户数
    if user_role == UserRoleEnum.coach.value:
        project_coachee_list = ProjectCoach.objects.filter(
            coach__user_id=coach_user.id,
            project__isnull=False,
            project__deleted=False,
            member__isnull=False,
            project_group_coach__isnull=True,
            deleted=False).exclude(member_id__in=interview_coachee_id_list)  # 排除掉掉有辅导记录的客户

        project_coachee_id_list = list(set(project_coachee_list.values_list('member_id', flat=True)))
        for item in project_coachee_list.all():
            # 查询项目未开始的
            if item.project.status == ProjectStatusEnum.not_sign.value:
                project_not_start.append(coach_customer_to_dict(item.member, coach_user.id,  UserRoleEnum.coachee.value))
            # 查询项目进行中的
            elif item.project.status == ProjectStatusEnum.progress.value:
                project_progress.append(coach_customer_to_dict(item.member, coach_user.id,  UserRoleEnum.coachee.value))
            else:
                # 剩余的认为是查询项目已结束的
                project_completed.append(coach_customer_to_dict(item.member, coach_user.id,  UserRoleEnum.coachee.value))

    # 已下单未预约辅导的客户需要展示在列表中。
    not_start_order = []
    if user_role in [UserRoleEnum.coach.value, UserRoleEnum.trainee_coach.value]:

        # 教练所有的C端订单
        all_order = Order.objects.filter(
            deleted=False, status=OrderStatusEnum.paid.value, public_attr__target_user_id=coach_user.id).exclude(
            # 排除有辅导的
            public_attr__user_id__in=interview_coachee_id_list).exclude(
            # 排除项目的
            public_attr__user_id__in=project_coachee_id_list).all()
        # 添加到数据列表
        order_user_ids = []
        for item in all_order:
            if item.public_attr.user_id not in order_user_ids:
                not_start_order.append(coach_customer_to_dict(
                    item.public_attr.user, coach_user.id, UserRoleEnum.trainee_coachee.value))
                # 去重处理
                order_user_ids.append(item.public_attr.user_id)

    # 合并同类型
    progress = interview_progress + project_progress
    not_start = interview_not_start + project_not_start
    completed = interview_completed + project_completed

    # 同类型根据用户名排序
    progress = sorted(progress, key=lambda i: lazy_pinyin(i['name']))
    not_start = sorted(not_start, key=lambda i: lazy_pinyin(i['name']))
    completed = sorted(completed, key=lambda i: lazy_pinyin(i['name']))
    not_start_order = sorted(not_start_order, key=lambda i: lazy_pinyin(i['name']))
    return [*progress, *not_start, *not_start_order, *completed]


def coach_customer_to_dict(coachee_user, coach_user_id, coachee_role):
    """
    将辅导用户转换为字典形式的数据
    :param coachee_user: 辅导用户对象
    :param coach_user_id: 教练用户的ID
    :param coachee_role: 辅导用户角色  UserRoleEnum C端用户优先使用昵称
    :return: 辅导用户的字典形式数据
    """
    # 获取辅导用户的访谈次数
    interview_count = interview_public.get_coachee_interview_count(coachee_user.id, coach_user_id)

    # 获取备注
    notes = get_coach_to_client_notes(coach_user_id, coachee_user.id)

    # 获取辅导用户的别名
    true_name = coachee_user.cover_name

    # 如果是个人用户，获取昵称
    if coachee_role == UserRoleEnum.trainee_coachee.value:
        personal_user = PersonalUser.objects.filter(user_id=coachee_user.id, deleted=False).first()
        if personal_user and personal_user.nickname:
            true_name = personal_user.nickname

    # 构建并返回用户数据的字典形式
    data = {
        'id': coachee_user.pk,  # 用户ID
        'name': true_name,  # 用户名
        'head_image_url': coachee_user.head_image_url,  # 用户头像URL
        'interview_count': interview_count,  # 访谈次数
        'company': get_coachee_company(coachee_user),  # 用户所在公司
        'name_letter': lazy_pinyin(true_name)[0][0].upper(), # 用户名的首字母
        'notes': notes  # 备注的昵称
    }
    return data


def get_coach_to_client_notes(coach_user_id, user_id):
    """
    获取教练对客户的备注信息。

    这个函数通过教练的用户 ID 和客户的用户 ID 来检索教练对客户的备注。
    如果找到相应的备注，就返回该备注；如果没有找到，就返回 None。

    参数:
    coach_user_id: 教练的用户 ID。
    user_id: 客户的用户 ID。

    返回:
    找到的备注信息或 None。
    """
    obj = CoachToClientNotes.objects.filter(
        coach__user_id=coach_user_id, user_id=user_id, deleted=False).first()
    if obj:
        return obj.notes
    return


def get_to_c_coachee_info(coach_user_id, coachee_user_id):
    """
    获取 C 端学员的信息。
    :param coach_user: 教练的用户对象。
    :param coachee_user: 学员的用户对象。
    :return 学员个人用户对象，如果没有找到有效的辅导项目，则返回None, None
    """

    # 如果约过C端辅导，才返回用户C端数据辅导数据
    if ProjectInterview.objects.filter(
            deleted=False, public_attr__user_id=coach_user_id, public_attr__target_user_id=coachee_user_id,
            type=INTERVIEW_TYPE_COACHING, public_attr__project__isnull=True).exclude(
        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).exists():

        personal_user = PersonalUser.objects.filter(user_id=coachee_user_id, deleted=False).first()
        if personal_user:
            return personal_user
    return None


def get_coachee_project_obj(coach_user, coachee_user):
    """
    获取教练用户和个人用户之间关联的项目对象。

    :param coach_user: 教练用户对象
    :param coachee_user: 个人用户（被教练的用户）对象
    :return: 如果找到关联的项目对象，返回该项目对象；否则返回None

    函数逻辑描述：
    1. 首先在ProjectCoach表中查找匹配的记录，这些记录应该满足以下条件：
       - 教练用户匹配
       - 项目存在且未被删除
       - 成员为个人用户
       - 不是项目组教练
       - 记录未被删除
    2. 如果找到匹配的ProjectCoach记录，返回关联的项目对象。
    3. 如果没有找到匹配的ProjectCoach记录，接着在ProjectInterview表中查找匹配的记录，这些记录应该满足以下条件：
       - 目标用户为个人用户
       - 用户为教练用户
       - 类型为辅导类型
       - 项目ID存在
    4. 如果找到匹配的ProjectInterview记录，返回关联的项目对象。
    5. 如果以上都没找到，返回None。
    """

    # 查找ProjectCoach表中的匹配记录
    # 先找到客户参与的项目
    coachee_project_list = list(ProjectMember.objects.filter(user=coachee_user, is_forbidden=False).values_list('project', flat=True))

    project_coach = ProjectCoach.objects.filter(
        coach__user=coach_user,
        project__deleted=False, member=coachee_user,
        project_group_coach__isnull=True, deleted=False, 
        project_id__in=coachee_project_list).first()
    
    if project_coach:
        return project_coach.project

    # 如果没有找到，查找ProjectInterview表中的匹配记录
    else:
        project_interview = ProjectInterview.objects.filter(
            public_attr__target_user=coachee_user, public_attr__user=coach_user,
            public_attr__project_id__isnull=False, public_attr__project_id__in=coachee_project_list,
        ).first()
        return project_interview.public_attr.project if project_interview else None


def work_wechat_coach_customer_info(work_wechat_coach_user, work_wechat_coachee_user, coachee_user):
    """
    处理企业微信中教练和学员的好友关系。

    :param work_wechat_coach_user: 教练的企业微信用户信息
    :param work_wechat_coachee_user: 学员的企业微信用户信息（可能为空）
    :param coachee_user: 学员的用户信息
    :return: 是否为好友关系的布尔值，以及学员的外部联系人ID（如果有）
    """
    # 默认情况下，假定学员不是教练的好友
    is_relevance = False
    external_user_id = None

    # 如果用户在企业微信关联表里面有数据, 可以使用关联表的external_user_id匹配user_external_user_id
    coachee_external_user_id = work_wechat_coachee_user.external_user_id if work_wechat_coachee_user else None

    # 主动异步刷新教练的客户列表
    task.update_work_wechat_user_customer_dict.delay(work_wechat_coach_user.wx_user_id)

    # 尝试从Redis中获取教练的好友列表缓存
    redis_customer_dict = third_party_redis.get(f'{work_wechat_coach_user.wx_user_id}_customer_dict')
    # 如果缓存存在且缓存数据有效
    if redis_customer_dict and None not in json.loads(redis_customer_dict.decode()).values():
        # 将缓存数据从字符串反序列化为字典
        redis_customer_dict = json.loads(redis_customer_dict.decode())

        # 遍历缓存中的好友列表
        for user_external_user_id, user_unionid in redis_customer_dict.items():
            # 如果找到匹配的unionid，则说明学员是教练的外部联系人
            if coachee_user.unionid == user_unionid or coachee_external_user_id == user_external_user_id:
                is_relevance = True  # 标记为好友
                external_user_id = user_external_user_id  # 更新外部联系人ID
                break  # 匹配成功后退出循环

    else:
        # 如果缓存不存在，直接调用企业微信API获取教练的所有外部联系人列表
        is_success, all_external_user_list = work_wechat.WorkWechat().get_external_user_list(
            [work_wechat_coach_user.wx_user_id])
        if is_success:
            # 如果API调用成功
            redis_data = {}  # 准备用于缓存的数据
            for item in all_external_user_list:

                wx_unionid = item.get('external_contact').get('unionid')
                wx_external_userid = item.get('external_contact').get('external_userid')

                # 收集需要缓存的数据
                redis_data[wx_external_userid] = wx_unionid

                # external_userid匹配成功，标记为好友，并返回
                if wx_external_userid == coachee_external_user_id:
                    is_relevance = True  # 标记为好友
                    external_user_id = wx_external_userid  # 更新外部联系人ID

                # wx_unionid和用户unionid都存在的情况，再一次进行比对
                elif wx_unionid and coachee_user.unionid:
                    if wx_unionid == coachee_user.unionid:
                        is_relevance = True  # 标记为好友
                        external_user_id = wx_external_userid  # 更新外部联系人ID

                # 如果是好友，更新数据信息
                if is_relevance:
                    with transaction.atomic():
                        # 如果学员已存在企业微信用户信息，则更新；否则创建新的记录
                        if work_wechat_coachee_user:
                            work_wechat_coachee_user.external_user_id = external_user_id
                            work_wechat_coachee_user.save()
                        else:
                            WorkWechatUser.objects.create(user_id=coachee_user.id, external_user_id=external_user_id)
            # 将新获取的数据缓存到Redis
            third_party_redis.set(f'{work_wechat_coach_user.wx_user_id}_customer_dict', json.dumps(redis_data))

    return is_relevance, external_user_id


def update_resume_to_coach_tag(coach_id):
    coach = Coach.objects.filter(id=coach_id, deleted=False).first()
    if not coach:
        return
    user = coach.user

    resume = Resume.objects.filter(
        deleted=False, coach_id=coach_id, is_customization=False).first()
    if not resume:
        return
    coach_course = resume.coach_course  # 受训经验
    coach_auth = resume.coach_auth  # 教练资质
    gender = user.gender  # 性别

    coach_course_parent_tag_name = '对教练的受训要求'
    coach_auth_parent_tag_name = '对教练的资质要求'
    coach_gender_parent_tag_name = '对教练的性别偏好'

    # 清理教练历史标签，重新创建标签信息
    TagObject.objects.filter(
        deleted=False, object_type=TagObjectTypeEnum.coach.value,
        object_id=coach_id, source__isnull=True,
        tag__parent__name__in=[coach_course_parent_tag_name, coach_auth_parent_tag_name, coach_gender_parent_tag_name]
    ).delete()

    from wisdom_v2.common.tag_public import tag_name_update_object_tag
    # 更新教练的受训要求标签
    if coach_course:
        if not isinstance(coach_course, list):
            coach_course = list(coach_course)

        for item in coach_course:
            tag_name = ResumeCoachCourseTypeEnum.get_resume_tag_name(item)
            if tag_name:
                tag_name_update_object_tag(
                    coach_id, TagObjectTypeEnum.coach.value, tag_name, coach_course_parent_tag_name)

    # 更新教练的资质要求标签
    if coach_auth:
        coach_auth_tag_name = CoachAuthEnum.get_resume_tag_name(coach_auth)
        tag_name_update_object_tag(
            coach_id, TagObjectTypeEnum.coach.value, coach_auth_tag_name, coach_auth_parent_tag_name)

    # 更新教练的性别偏好标签
    if gender:
        coach_gender_tag_name = GENDER_TYPE.get(int(gender))

        tag_name_update_object_tag(
            coach_id, TagObjectTypeEnum.coach.value, coach_gender_tag_name, coach_gender_parent_tag_name)
