from utils.message.lark_message import LarkMessageCenter
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum
from wisdom_v2.common import project_public
from wisdom_v2.models import UserAdditionalInfo
from wisdom_v2.common import user_public
from utils import user_data_collection_constant


def get_options_by_sub(sub):
    """
    根据提供的题目编号（sub），从用户数据集合中检索相应的选项列表。

    参数:
        sub (int): 要查找的题目编号。

    返回:
        list: 与给定题目编号对应的选项列表，如果没有找到则返回None。
    """

    # 包含题目和选项信息的列表。
    user_data_collection_topic = user_data_collection_constant.get_project_user_data_collection_topic(True, False)

    # 遍历用户数据集合中的每一个题目信息字典
    for topic_info in user_data_collection_topic:
        # 检查当前字典中的'sub'字段是否与给定的sub值匹配
        if topic_info["sub"] == sub:
            # 如果匹配，返回该字典中的'options'字段，即选项列表
            return topic_info["options"]

    # 如果循环结束都没有找到匹配的sub值，返回None表示没有找到
    return None


# 同步用户信息到标签
# 发送信息到飞书消息
def sync_info_to_tag(user_id):
    if not user_id:
        return
    all_project_member = user_public.get_user_to_project_member_all(user_id)
    if all_project_member:
        project_member = all_project_member[0]
        user_data = UserAdditionalInfo.objects.get(user_id=user_id).all_info

        tag_ids_list = user_data_collection_constant.project_user_data_to_tag_list(user_data)
        # 有需要更新的标签数据，则更新。
        if tag_ids_list:
            project_public.update_project_member_tag(project_member.id, tag_ids_list)

        # 触发飞书通知
        # 将问卷问题和user_data对齐，生成完整的题目和回答数据
        user_data_collection_topic = user_data_collection_constant.get_project_user_data_collection_topic(True, False)
        content = "\n".join([f"{next((topic['topic'] for topic in user_data_collection_topic if topic['sub'] == data['sub']), '未知题目')}: {data['count']}" for data in user_data])
        url = f'https://admin.qzcoach.com/#/projectManage/project/project_details?project_id={project_member.project_id}&isEdit=true'
        params = {
            'project_name': f'{project_member.project.full_name}',
            'user_name': project_member.user.cover_name,
            'url': url,
            'content': content
        }
        # 使用修改后的飞书消息发送方法
        LarkMessageCenter().send_business_message(
            params,
            LarkMessageTypeEnum.user_info_collect.value,
            project_id=project_member.project_id,
            sender_id=project_member.user_id
        )