from datetime import datetime

import pendulum
from dateutil.relativedelta import relativedelta
from django.db.models import Sum
from rest_framework import serializers

from utils.utc_date_time import get_total_time
from wisdom_v2.app_views.app_coach_actions import CoachTaskSerializer
from wisdom_v2.common import coach_task_public
from wisdom_v2.enum.project_interview_enum import DataType, ProjectInterviewTypeEnum
from wisdom_v2.models import EvaluationReport, GrowthGoals, ProjectInterview, EvaluationModule, PublicAttr, GrowthGoalsChange, \
    GrowthGoals2ChangeObservation
from wisdom_v2.views.constant import ATTR_TYPE_EVALUATION_REPORT, ATTR_TYPE_INTERVIEW, LBI_EVALUATION, ATTR_TYPE_EVALUATION_ANSWER
from utils.api_response import WisdomValidationError


class AppCoacheeGrowthGoalsViewSerializers(serializers.ModelSerializer):

    coach_user_id = serializers.SerializerMethodField(help_text='教练id')
    coachee_name = serializers.CharField(source='public_attr.user.cover_name', read_only=True, required=False)
    coachee_user_id = serializers.SerializerMethodField(help_text='被教练者id')
    change = serializers.SerializerMethodField(help_text='达成目标后发生的改变')
    start_time = serializers.DateTimeField(format='%Y.%m.%d', source='public_attr.start_time', required=False)
    end_time = serializers.DateTimeField(format='%Y.%m.%d', source='public_attr.end_time', required=False)
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', required=False, read_only=True)

    class Meta:
        model = GrowthGoals
        exclude = ('updated_at', 'public_attr')

    def get_coach_user_id(self, obj):
        if obj.public_attr.target_user:
            return obj.public_attr.target_user.id
        return

    def get_coachee_user_id(self, obj):
        if obj.public_attr.user:
            return obj.public_attr.user.id
        return

    def get_change(self, obj):
        if obj.growth_goals_change.filter(deleted=False).exists():
            return [{'id': change.pk, 'content': change.content} for change in
                    obj.growth_goals_change.filter(deleted=False)]
        return

    def update(self, instance, validated_data):
        change = validated_data.get('change', None)
        month = validated_data.get('month', None)
        content = validated_data.get('content', None)
        deleted = validated_data.get('deleted', False)
        is_update = False if GrowthGoals2ChangeObservation.objects.filter(
            growth_goals=instance, deleted=False).exists() else True
        if content:
            if not is_update and content != instance.content:
                raise WisdomValidationError('当前成长目标已配置改变观察反馈，如需修改请联系项目运营')

        if change:
            # 已经配置改变观察的成长目标不能修改
            # 检查content长度
            to_long = [1 if len(c['content']) > 200 else 0 for c in change]
            if 1 in to_long:
                raise WisdomValidationError('转变行为限制200字以内')

            new_ids = [c['id'] for c in change if 'id' in c.keys()]
            new_content = [c['content'] for c in change if 'id' not in c.keys()]
            exists_ids = list(GrowthGoalsChange.objects.filter(growth_goals=instance,
                                                               deleted=False).values_list('id', flat=True))
            # add_list = list(set(new_ids).difference(set(exists_ids)))

            deleted_list = list(set(exists_ids).difference(set(new_ids)))
            if deleted_list:
                if not is_update:
                    raise WisdomValidationError('当前成长目标已配置改变观察反馈，如需修改请联系项目运营')
                GrowthGoalsChange.objects.filter(id__in=deleted_list).update(deleted=True)

            if new_content:
                if not is_update:
                    raise WisdomValidationError('当前成长目标已配置改变观察反馈，如需修改请联系项目运营')
                growth_goals_change_list = [GrowthGoalsChange(content=content, growth_goals=instance)
                                            for content in new_content]
                GrowthGoalsChange.objects.bulk_create(growth_goals_change_list)

            for data in change:
                if 'id' in data.keys():
                    change = GrowthGoalsChange.objects.filter(pk=data['id']).first()
                    if not is_update and change.content != data['content']:
                        raise WisdomValidationError('当前成长目标已配置改变观察反馈，如需修改请联系项目运营')
                    change.content = data['content']
                    change.save()

        if month and month != instance.month:
            end_time = instance.created_at + relativedelta(months=month)
            public_attr = instance.public_attr
            public_attr.end_time = end_time
            public_attr.save()

        if deleted and not is_update:
            raise WisdomValidationError('当前成长目标已配置改变观察反馈，如需修改请联系项目运营')

        return super().update(instance=instance, validated_data=validated_data)


class AppCoacheeGrowthNodeCoachTaskViewSerializers(CoachTaskSerializer):
    write_role = serializers.SerializerMethodField(help_text='填写记录的角色 | 1：教练；2：被教练者; 3: 教练及被教练者 4: 利益相关者')
    title = serializers.SerializerMethodField(help_text='模板标题')
    coach_task_type = serializers.SerializerMethodField(help_text='教练任务类型')
    coach_task_state = serializers.SerializerMethodField(help_text='教练任务状态 1：未开启，2：教练未填写，3：已开启，4：已完成')
    describe = serializers.SerializerMethodField(help_text='描述')
    query_type = serializers.SerializerMethodField(help_text='任务详情查看类型 1-查看报告 2-查看详情')

    def get_query_type(self, obj):
        is_summary_report_status = coach_task_public.check_coach_task_is_stakeholder_interview_summary(obj.id)
        return 1 if is_summary_report_status else 2  # 1-查看报告 2-查看详情

    def get_coach_task_type(self, obj):
        return obj.type

    def get_title(self, obj):
        if obj.template:
            return obj.template.title
        return

    def get_write_role(self, obj):
        if obj.template:
            return obj.template.write_role
        return

    def get_coach_task_state(self, obj):

        time = pendulum.now().strftime('%Y-%m-%d %H:%M:%S')
        coachee_time_time = ProjectInterview.objects.filter(
            public_attr__end_time__lt=time, deleted=False,
            public_attr__target_user=obj.public_attr.target_user,
            public_attr__type=ATTR_TYPE_INTERVIEW,
            type=ProjectInterviewTypeEnum.formal_interview
        ).exclude(public_attr__status=6).aggregate(tutoring_time=Sum('times'))['tutoring_time']
        coachee_time_time = coachee_time_time if coachee_time_time else 0

        if obj.template:
            # 教练没写报告并且学员辅导时常不够
            if coachee_time_time / 60 < obj.hours and self.get_state(obj) in [2, 3]:
                return 1
            # 辅导记录只需要教练填写
            if obj.template.write_role == 1:
                # 教练未填写
                if self.get_state(obj) in [2, 3]:
                    return 2
                else:
                    return 4
            # 辅导记录需要双方都填写
            if obj.template.write_role == 3:
                # 被教练者未填写
                if self.get_state(obj) in [3, 1]:
                    return 3
                else:
                    return 4
        return

    def get_describe(self, obj):

        hours_float = float(obj.hours)
        if hours_float.is_integer():
            hours = str(int(hours_float))  # 浮点数小数部分为0时，转换为整数
        else:
            hours = str(hours_float)  # 保持为浮点数

        if self.get_coach_task_state(obj) == 1:
            return '辅导{}小时后开启'.format(hours)
        elif self.get_coach_task_state(obj) == 2:
            return '教练填写后可查看报告'
        elif self.get_coach_task_state(obj) == 3:
            return '已开启，请前往填写'
        elif self.get_coach_task_state(obj) == 4:
            return '已完成'
        else:
            return '已完成'


class AppCoacheeGrowthNodeEvaluationModuleViewSerializers(serializers.ModelSerializer):
    name = serializers.CharField(source='evaluation.name', help_text='测评名称')
    evaluation_code = serializers.CharField(source='evaluation.code', help_text='测评编号')
    image_url = serializers.CharField(source='evaluation.image_url', help_text='配图链接')
    project_id = serializers.CharField(source='project_bundle.project.id', help_text='项目id')
    coachee_name = serializers.CharField(source='project_bundle.project_member.user.cover_name', help_text='学员姓名')
    data_type = serializers.SerializerMethodField(help_text='数据类型 1：辅导记录(该接口暂无)，2：教练任务，3:测评报告')
    describe = serializers.SerializerMethodField(help_text='描述')
    evaluation_report_id = serializers.SerializerMethodField(help_text='测评报告id')
    evaluation_module_state = serializers.SerializerMethodField(help_text='测评状态 1：未开启，2：已开启，3：已完成')
    is_user_fills_in_lbi_evaluation = serializers.SerializerMethodField()
    evaluation_report_type = serializers.SerializerMethodField(help_text='测评报告类型')

    def get_data_type(self, obj):
        return DataType.evaluation

    def get_evaluation_module_state(self, obj):
        evaluation_module = self.context

        if obj.id in evaluation_module.keys():
            return 3

        if pendulum.now().date() < obj.start_time:
            return 1

        if pendulum.now().date() >= obj.start_time:
            return 2
        return

    def get_describe(self, obj):

        if self.get_evaluation_module_state(obj) == 1:
            date = get_total_time(
                pendulum.now().strftime('%Y-%m-%d'), obj.start_time.strftime('%Y-%m-%d'))
            return '{}天后开启'.format(date.day)
        elif self.get_evaluation_module_state(obj) == 2:
            return '已开启，请前往填写'
        elif self.get_evaluation_module_state(obj) == 3:
            return '已完成'
        else:
            return

    # TODO: 目前只能在项目中按照测评id查找报告，如果测评id相同，则会返回第一个报告id
    def get_evaluation_report_id(self, obj):
        evaluation_report = EvaluationReport.objects.filter(
            deleted=False,
            public_attr__type=ATTR_TYPE_EVALUATION_REPORT,
            public_attr__project_id=obj.project_bundle.project_id,
            public_attr__user_id=obj.project_bundle.project_member.user_id,
            evaluation_id=obj.evaluation_id)
        if evaluation_report.exists():
            return evaluation_report.first().pk
        return

    def get_is_user_fills_in_lbi_evaluation(self, obj):
        if obj.evaluation.code == LBI_EVALUATION:
            public_attr = PublicAttr.objects.filter(
                project=obj.project_bundle.project,
                user=obj.project_bundle.project_member.user,
                target_user=obj.project_bundle.project_member.user,
                type=ATTR_TYPE_EVALUATION_ANSWER
            ).first()
            if public_attr:
                return True
        return False

    def get_evaluation_report_type(self, obj):
        evaluation_report_config = obj.evaluation.evaluation_report_config.filter(deleted=False).first()
        if evaluation_report_config:
            return evaluation_report_config.type
        return

    class Meta:
        model = EvaluationModule
        exclude = ('updated_at', 'created_at',
                   'start_time', 'end_time', 'send_email', 'deleted', 'is_submit')

