import datetime
import logging
import re

import pendulum
import redis
from django.conf import settings
from celery import shared_task
from django.db.models import Sum
from django.utils import timezone

from data import models
from data.models import AppMessagetemplates
from utils import null_or_blank, aesdecrypt
from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils.message import email as message_email
from utils.messagecenter.email import Email
from utils.messagecenter.innermessage import Innermessage
from utils.messagecenter.sms import SMS
from utils.messagecenter.actions import get_template_content1, get_template_content2
from utils.feishu_robot import push_celery_hanging_message, push_wx_error_message
from utils.qr_code import get_qr_code

from celery.exceptions import SoftTimeLimitExceeded

from utils.utc_date_time import get_total_time
from wisdom_v2.enum.project_enum import SendDelayEmailEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum
from wisdom_v2.enum.service_content_enum import Coach<PERSON>ype<PERSON>num
from wisdom_v2.enum.user_backend_enum import UserBackendTypeEnum
from wisdom_v2.models import EvaluationModule, ProjectInterview, UserBackend, Project, ProjectMember, OneToOneCoach, \
    ProjectInterested, User
from utils.message.lark_message import LarkMessageCenter

api_action_logging = logging.getLogger('api_action')


@shared_task(queue='task', ignore_result=True, soft_time_limit=15)
def push_custom_message(user, msg, title):
    try:
        if not user:
            return None
        if not title or not msg:
            return None
        msg_email = msg.replace('\n', '<br />')
        email = Email(title, msg_email, [user.Email])
        try:
            email.send()
        except Exception as e:
            pass

        sms = SMS()
        try:
            sms.sendMsg(user.PhoneNo, msg)
        except Exception as e:
            pass

        innermessage = Innermessage(title, msg, 'custom', user, fromuser=None)
        innermessage.send()
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('push_custom_message')


@shared_task(queue='task', ignore_result=True, soft_time_limit=20)
def push_message(user, template_name, param, project_id=None):
    try:
        if not user:
            return None
        email_disable = False
        sms_disable = False
        if user.Role_Id == 4:
            coach_info = models.AppCoach.objects.filter(User_Id=user.User_Id).first()
            if coach_info:
                email_disable = coach_info.Sendemail == 0
                sms_disable = coach_info.Sendmsg == 0
        if null_or_blank(template_name):
            return None
        template = models.AppMessagetemplates.objects.filter(MessageType=template_name).first()
        if not template:
            return None

        # 发送邮件都是进行中的项目
        if project_id and models.AppProbject.objects.filter(Probject_Id=project_id, Endtime__lte=timezone.now()).exists():
            return None

        if user:
            param["userid"] = user.User_Id
            param["truename"] = user.UserTrueName
            param["username"] = user.UserName
            param["useremail"] = user.Email
            param["usermobile"] = user.PhoneNo
            param["password"] = aesdecrypt(user.UserPwd)
            if models.AppMember.objects.filter(User_Id=user.User_Id).exists():
                gender = models.AppMember.objects.filter(User_Id=user.User_Id).first().Sex
            elif models.AppCoach.objects.filter(User_Id=user.User_Id).exists():
                gender = models.AppCoach.objects.filter(User_Id=user.User_Id).first().Sex
            else:
                gender = None
            param["gender"] = settings.GENDER[gender] if gender else '先生/女士'

        param['siteurl'] = settings.SITE_URL
        param['template_content1'] = get_template_content1()
        if project_id:
            param['template_content2'] = get_template_content2(project_id)
        if 'url' not in param:
            param['url'] = settings.SITE_URL
        param['sitename'] = settings.SITE_NAME

        if template.SendEmail == 1 and not email_disable:
            subject = replace_content(template.EmailSubject, param)
            body = replace_content(template.EmailBody, param)
            email = Email(subject, body, [user.Email])
            try:
                email.send()
            except Exception as e:
                pass
        if project_id and project_id not in settings.PROJECT_BLACK_LIST and template.SendSMS == 1 and not sms_disable:
            pass
            # 不发短信
            # body = replace_content(template.SMSBody, param)
            # sms = SMS()
            # try:
            #     sms.sendMsg(user.PhoneNo, body)
            # except Exception as e:
            #     for _ in range(3):
            #         time.sleep(1)
            #         sms.sendMsg(user.PhoneNo, body)

        if template.SendInnerMessage == 1:
            subject = replace_content(template.InnerMessageSubject, param)
            body = replace_content(template.InnerMessageBody, param)
            innermessage = Innermessage(subject, body, template_name, user, fromuser=None)
            innermessage.send()
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('push_message')


def custom_send(subject, content, param):
    return None


def replace_content(content, param):
    if null_or_blank(content):
        return ''
    if param:
        for k,v in param.items():
            content = content.replace('$'+k+'$', str(v))
    content = replace_sys(content)
    content = re.sub('\$\w+\$', '', content)
    return content


def replace_sys(content):
    if null_or_blank(content):
        return ''
    sitename = ''
    siteurl = ''
    content = content.replace('$sitename$', sitename)
    content = content.replace('$siteurl$', siteurl)
    return content


def v2_template_content():
    return """ 群智企业教练 激发个体潜能 汇聚群体智慧 获取帮助：<EMAIL> 地址：北京市朝阳区电子城ICPIC创新中心-E座 https://www.cw-coach.com """


@shared_task(queue='task', ignore_result=True, soft_time_limit=20)
def push_v2_message(user, template_name, param=None, project_id=None, copy_email=None):
    try:
        if not user or not user.send_email or not template_name or not user.email:
            return
        # if user.work_wechat_user.filter(deleted=False).exists():
        #     return

        template = models.AppMessagetemplates.objects.filter(MessageType=template_name).first()
        if not template:
            return None

        if param is None:
            param = {}

        if user:
            param["true_name"] = user.cover_name
            param["name"] = user.name
            param["email"] = user.email
            param["openid"] = user.openid
            param["phone"] = user.phone
            param["password"] = aesdecrypt(user.password)
            param["gender"] = settings.GENDER[user.gender] if user.gender else '先生/女士'

        param['siteurl'] = settings.SITE_URL
        param['template_content'] = v2_template_content()
        if 'url' not in param:
            param['url'] = settings.SITE_URL
        param['sitename'] = settings.SITE_NAME


        message_email.message_send_email_base(template_name, param, [user.email], copy_email, project_id=project_id,
                                             receiver_ids=user.id)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('push_v2_message')


@shared_task(queue='task', ignore_result=True, soft_time_limit=20)
def send_scoring_risk_alert_email(
        message_type, describe, interview_id):
    try:
        try:
            interview = ProjectInterview.objects.get(pk=interview_id)
            users = UserBackend.objects.filter(
                deleted=False, project=interview.public_attr.project.id,
                role__name='项目运营',
                user_type=UserBackendTypeEnum.admin.value).all()
            email = [item.user.email for item in users]
            param = {
                'project_name': interview.public_attr.project.name,
                'company_name': interview.public_attr.project.company.short,
                'coach_name': interview.public_attr.user.cover_name,
                'coachee_name': interview.public_attr.target_user.cover_name,
                'date': interview.public_attr.start_time.strftime('%y年%m月%d日'),
                'describe': describe
            }

            message_email.message_send_email_base('send_scoring_risk_alert_email', param, email,
                project_id=interview.public_attr.project.id, receiver_ids=users.values_list('user_id', flat=True))
        except Exception as e:
            api_action_logging.info({'name': 'send_scoring_risk_alert_email',
                                        'error': str(e),
                                        'data': {
                                            'describe': describe,
                                            'interview_id': interview_id,
                                        }})
            return

    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_scoring_risk_alert_email')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_scoring_risk_alert_email:{str(e)}'})



@shared_task(queue='default', ignore_result=True)
def send_evaluation_message(project_member_id, evaluation_module_id, project_interested_id=None):
    try:
        project_member = ProjectMember.objects.get(pk=project_member_id)
        evaluation_module = EvaluationModule.objects.get(pk=evaluation_module_id)
        # 获取项目管理员信息
        project_backend = UserBackend.objects.filter(project_id=project_member.project_id,
                                                     user_type=UserBackendTypeEnum.admin.value,
                                                     role__name='项目运营', deleted=False).first()
        if project_interested_id:
            project_interested = ProjectInterested.objects.filter(pk=project_interested_id, deleted=False)
        else:
            project_interested = ProjectInterested.objects.filter(master_id=project_member.user_id,
                                                                  project_id=project_member.project_id,
                                                                  deleted=False)
        if project_interested.exists():
            from utils.wechat_oauth import WeChatMiniProgram
            # todo 优化发送路径
            for stakeholder in project_interested:
                # 生成短链接
                url_state, short_link = WeChatMiniProgram().get_url_link(
                    'pages_evaluation/feedback_list/feedback_list',
                    f'stakeholder_id={stakeholder.interested_id}&project_id={project_member.project_id}')

                data = f'{settings.SITE_URL}feedback/?stakeholder_id={stakeholder.interested_id}&project_id={project_member.project_id}'
                qr_code = get_qr_code(data)

                param = {}
                param['name'] = stakeholder.interested.cover_name
                param['member_name'] = project_member.user.cover_name
                param['date'] = evaluation_module.end_time.strftime('%Y年%m月%d日')
                param['project_manage_name'] = project_backend.user.cover_name
                param['email'] = project_backend.user.email
                param['qr_code'] = qr_code
                param['short_link'] = short_link
                param['evaluation_name'] = evaluation_module.evaluation.name
                message_email.message_send_email_base('stakeholder_evaluation_message', param, [stakeholder.interested.email],
                                                      project_id=project_member.project_id, receiver_ids=stakeholder.interested.id)
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_evaluation_message')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_evaluation_message:{str(e)}'})
