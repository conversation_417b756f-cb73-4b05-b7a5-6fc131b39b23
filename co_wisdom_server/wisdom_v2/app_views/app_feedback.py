from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum
from rest_framework.views import APIView
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from utils.api_response import success_response, parameter_error_response
from ..models import FeedBack, FeedBackImage, User
from utils.message.lark_message import LarkMessageCenter
from django.db import transaction
from celery import shared_task

# 添加异步任务
@shared_task
def send_feedback_notification(content, user_name=None, user_id=None):
    content_data = {
        'content': content,
        'user_name': user_name
    }
    # 使用修改后的飞书消息发送方法
    LarkMessageCenter().send_business_message(
        content_data,
        LarkMessageTypeEnum.app_feedback.value,
        sender_id=user_id
    )

class FeedBackView(APIView):
    authentication_classes = []

    @swagger_auto_schema(
        operation_id='创建意见反馈',
        operation_summary='创建意见反馈',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
                'content': openapi.Schema(type=openapi.TYPE_STRING, description='反馈内容'),
                'images': openapi.Schema(type=openapi.TYPE_ARRAY, description='反馈图片url',
                                         items=openapi.Schema(type=openapi.TYPE_STRING)),
            }
        ),
        tags=['app意见反馈']
    )
    def post(self, request, *args, **kwargs):
        try:
            user_id = int(request.data.get('user_id', 0))
            content = request.data.get('content', None)
            images = request.data.get('images', [])
            user = User.objects.filter(pk=user_id, deleted=False).first()
        except Exception:
            return parameter_error_response('请求参数错误')

        data = {'content': content}
        if user:
            data['creator_user'] = user

        with transaction.atomic():
            feedback = FeedBack.objects.create(**data)
            if images:
                for image in images:
                    FeedBackImage.objects.create(url=image, feedback=feedback)

            # 异步发送飞书通知
            send_feedback_notification.delay(content, user.name if user else None, user_id if user else None)

        return success_response()

