import datetime
from django.db.models import Sum
from utils.message.lark_message import LarkMessageCenter
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum

from utils import task
from wisdom_v2.enum.business_order_enum import SettlementUserTypeEnum, BusinessOrderSettlementStatusEnum, \
    BusinessOrderWithdrawalStatusEnum, BusinessOrderTypeEnum, BusinessOrderDataTypeEnum
from wisdom_v2.models import ProjectInterview
from wisdom_v2.models_file import BusinessSettlement


def generate_business_settlements(coach, business_order, user):
    """
    生成业务结算单
    :param coach: 教练
    :param business_order:  订单
    :param user: 提现发起人用户
    """
    enterprise_exists, public_course_exists, personal_exists = False, False, False
    for order in business_order:
        if order.type == BusinessOrderTypeEnum.personal:
            personal_exists = True
        elif order.type == BusinessOrderTypeEnum.enterprise:
            enterprise_exists = True
        elif order.type == BusinessOrderTypeEnum.public_course:
            public_course_exists = True
    if enterprise_exists and not public_course_exists and not personal_exists:
        user_type = SettlementUserTypeEnum.enterprise
    elif not enterprise_exists and not personal_exists and public_course_exists:
        user_type = SettlementUserTypeEnum.enterprise
    elif not enterprise_exists and not public_course_exists and personal_exists:
        user_type = SettlementUserTypeEnum.personal
    else:
        user_type = SettlementUserTypeEnum.others

    apply_withdrawal_amount = business_order.aggregate(apply_withdrawal_amount=Sum('coach_actual_income'))
    apply_withdrawal_amount = apply_withdrawal_amount.get('apply_withdrawal_amount', 0) \
        if apply_withdrawal_amount.get('apply_withdrawal_amount', 0) else 0

    member_paid = business_order.aggregate(member_paid=Sum('member_paid'))
    member_paid = member_paid.get('member_paid', 0) if member_paid.get('member_paid', 0) else 0

    platform_service_amount = business_order.aggregate(platform_service_amount=Sum('platform_service_amount'))
    platform_service_amount = platform_service_amount.get('platform_service_amount', 0) \
        if platform_service_amount.get('platform_service_amount', 0) else 0

    tax_amount = business_order.aggregate(tax_amount=Sum('tax_amount'))
    tax_amount = tax_amount.get('tax_amount', 0) if tax_amount.get('tax_amount', 0) else 0

    business_settlement = BusinessSettlement.objects.create(
        user_type=user_type, apply_withdrawal_amount=apply_withdrawal_amount, member_paid=member_paid,
        apply_time=datetime.datetime.now(), platform_service_amount=platform_service_amount,
        tax_amount=tax_amount, create_user=user,
        settlement_status=BusinessOrderSettlementStatusEnum.unsettled, coach=coach)
    for order in business_order:
        order.business_settlement = business_settlement
        order.withdrawal_status = BusinessOrderWithdrawalStatusEnum.withdrawn
        order.save()

    # 只发送订单金额大于0的结算消息
    if apply_withdrawal_amount > 0:
        data = {
            "coach_name": coach.user.true_name,
            "create_name": user.true_name,
            "type": SettlementUserTypeEnum(user_type).describe(),
            "order_ids": [order.id for order in business_order],
        }
        LarkMessageCenter().send_business_message.delay(data, LarkMessageTypeEnum.coach_settlement_create.value)

    return business_settlement


def mark_settlement_status(business_settlements, user):
    """
    标记结算状态
    :param business_settlements: 结算单对象
    :param user:  结算处理人
    """
    settlement_time = datetime.datetime.now()

    # 只有教练自己申请提现的订单，才发结算消息
    send_notice_settlement = []
    for business_settlement in business_settlements:
        business_settlement.settlement_status = BusinessOrderSettlementStatusEnum.settled
        business_settlement.processor = user
        business_settlement.settlement_time = settlement_time
        business_settlement.save()

        # 通知只计算教练申请提现的结算单金额
        if business_settlement.create_user_id == business_settlement.coach.user_id:
            send_notice_settlement.append(business_settlement)

        business_orders = business_settlement.business_order.filter(deleted=False)
        for business_order in business_orders:
            business_order.settlement_status = BusinessOrderSettlementStatusEnum.settled
            business_order.settlement_time = settlement_time
            business_order.save()
            if business_order.type in [BusinessOrderTypeEnum.enterprise, BusinessOrderTypeEnum.personal]:
                business_order2object = business_order.business_order2object.filter(deleted=False).first()
                # 项目结算来源的订单无interview数据，无需修改
                if business_order2object and business_order2object.data_type == BusinessOrderDataTypeEnum.interview.value:
                    interview = ProjectInterview.objects.filter(id=business_order2object.object_id).first()
                    if interview:
                        interview.is_settlement = True
                        interview.save()
    if send_notice_settlement:
        task.send_coach_settlement_money_info.delay(send_notice_settlement)
