import datetime
from decimal import Decimal
from rest_framework import serializers
from django.db import transaction

from wisdom_v2.common import coach_public
from wisdom_v2.common import interview_public
from wisdom_v2.common import business_order_public
from wisdom_v2.enum.business_order_enum import BusinessOrderTypeEnum, BusinessOrderDurationTypeEnum, WorkTypeEnum, \
    BusinessOrderPayStatusEnum, BusinessOrderDataTypeEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceTypeEnum
from wisdom_v2.enum.service_content_enum import ActivityTypeEnum, CoachOfferStatusEnum
from wisdom_v2.models_file import BusinessOrder, BusinessOrder2Object
from wisdom_v2.models import Coach, CoachOffer, UserContract, ProjectInterview
from wisdom_v2.models_file.business_order import BusinessOrderExtraCost
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, NON_ORDER_TAX_POINT, NON_PLATFORM_SERVICE_SCALE, PLATFORM_SERVICE_SCALE


class BusinessOrderSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id')
    type = serializers.IntegerField(help_text='工作类型')
    work_type = serializers.IntegerField(help_text='工作形式')
    project_name = serializers.SerializerMethodField(help_text='项目名称')
    member_name = serializers.SerializerMethodField(help_text='客户名称')
    class_name = serializers.SerializerMethodField(help_text='班级名称')
    coach_name = serializers.CharField(source='coach.user.true_name', help_text='教练名称')
    personal_name = serializers.CharField(source='coach.personal_name', help_text='对个人用户可见姓名')
    coach_price = serializers.SerializerMethodField(help_text='教练单价')
    work_time = serializers.SerializerMethodField(help_text='工作时间')
    duration = serializers.SerializerMethodField(help_text='结算时长')
    member_paid = serializers.SerializerMethodField(help_text='客户实付')
    pay_status = serializers.IntegerField(help_text='支付状态')
    withdrawal_status = serializers.IntegerField(help_text='提现状态')
    settlement_status = serializers.IntegerField(help_text='结算状态')
    pay_time = serializers.DateTimeField(help_text='支付时间', format='%Y-%m-%d %H:%M:%S')
    settlement_time = serializers.DateTimeField(help_text='结算时间', format='%Y-%m-%d %H:%M:%S')
    platform_scale = serializers.SerializerMethodField(help_text='平台扣除比例')
    platform_service_amount = serializers.SerializerMethodField(help_text='平台服务费')
    tax_point = serializers.SerializerMethodField(help_text='税点')
    tax_amount = serializers.SerializerMethodField(help_text='税费')
    coach_actual_income = serializers.SerializerMethodField(help_text='教练实际收益')
    history_record = serializers.SerializerMethodField(help_text='变更记录')
    apply_settlement_time = serializers.SerializerMethodField(help_text='申请提现时间')
    project_offer_id = serializers.SerializerMethodField(help_text='教练offer')
    contract_url = serializers.SerializerMethodField(help_text='教练合同链接')
    order_count = serializers.SerializerMethodField(help_text='购买小时数')
    used_order_count = serializers.SerializerMethodField(help_text='已核销小时数')
    company_name = serializers.SerializerMethodField(help_text='公司简称')
    extra_cost_count = serializers.SerializerMethodField(help_text='额外费用数量')
    total_extra_cost = serializers.SerializerMethodField(help_text='费用总金额')
    order_promoter = serializers.SerializerMethodField(help_text='订单发起者')
    activity_theme = serializers.SerializerMethodField(help_text='活动主题')
    place_type = serializers.SerializerMethodField(help_text='辅导地址类型')
    interview_number = serializers.SerializerMethodField(help_text='辅导次数', read_only=True)


    class Meta:
        model = BusinessOrder
        fields = ('id', 'type', 'work_type', 'project_name', 'member_name', 'class_name', 'coach_name', 'coach_price',
                  'work_time', 'duration', 'member_paid', 'pay_status', 'withdrawal_status', 'settlement_status',
                  'pay_time', 'settlement_time', 'platform_scale', 'platform_service_amount', 'tax_point', 'tax_amount',
                  'coach_actual_income', 'history_record', 'apply_settlement_time', 'project_offer_id', 'contract_url',
                  'order_count', 'used_order_count', 'personal_name', 'company_name', 'extra_cost_count',
                  'total_extra_cost', 'order_promoter', 'activity_theme', 'place_type', 'interview_number')


    def get_interview_number(self, obj):
        bo2o = BusinessOrder2Object.objects.filter(business_order=obj, deleted=False).first()
        if bo2o and bo2o.data_type == BusinessOrderDataTypeEnum.interview.value:
            interview = ProjectInterview.objects.filter(id=bo2o.object_id, deleted=False).first()
            if interview:
                return interview_public.get_interview_number_by_obj(interview, describe=True)

    
    def get_place_type(self, obj):
        if obj.work_type in WorkTypeEnum.interview_data():
            bo2o = BusinessOrder2Object.objects.filter(
                business_order=obj, deleted=False, data_type=BusinessOrderDataTypeEnum.interview.value).first()
            if bo2o:
                # 根据 BusinessOrder2Object 实例中的 object_id 查找对应的未被删除的 ProjectInterview 实例
                project_interview = ProjectInterview.objects.filter(
                    id=bo2o.object_id, deleted=False, place_type__isnull=False).first()
                if project_interview:
                    return ProjectInterviewPlaceTypeEnum.get_display(project_interview.place_type)
        return '--'

    def get_activity_theme(self, obj):
        activity = business_order_public.get_business_order_activity(obj)
        if activity:
            return activity.theme
        return '--'

    def get_order_promoter(self, obj):
        if obj.type == BusinessOrderTypeEnum.enterprise.value:
            if obj.project:
                manager_true_name = [item.get('true_name') for item in obj.project.manager_list]
                return '；'.join(manager_true_name)
            return

        elif obj.type == BusinessOrderTypeEnum.personal.value:
            return obj.coach.user.cover_name

        elif obj.type == BusinessOrderTypeEnum.public_course.value:
            public_course_coach = business_order_public.get_business_order_to_public_course(obj)
            if public_course_coach:
                # 公开课教练 - 公开课 - 公开课的创建人 - 姓名
                return public_course_coach.public_courses.creator.cover_name
            return

        else:
            return


    def get_extra_cost_count(self, obj):
        return BusinessOrderExtraCost.objects.filter(business_order=obj, deleted=False).count()

    def get_total_extra_cost(self, obj):
        return f'{business_order_public.get_all_business_order_extra_cost(obj)}元'


    def get_company_name(self, obj):
        if obj.project:
            return obj.project.company.real_name

    def get_order_count(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            relation = BusinessOrder2Object.objects.filter(business_order_id=obj.id, type=WorkTypeEnum.one_to_one).first()
            if relation:
                interview = ProjectInterview.objects.filter(id=relation.object_id).first()
                if interview:
                    order = interview.order
                    return order.count

    def get_used_order_count(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            relation = BusinessOrder2Object.objects.filter(business_order_id=obj.id, type=WorkTypeEnum.one_to_one).first()
            if relation:
                interview = ProjectInterview.objects.filter(id=relation.object_id).first()
                if interview:
                    order = interview.order
                    interviews = order.interview.filter(
                        deleted=False).exclude(
                        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by(
                        'public_attr__start_time').values_list('id', flat=True)
                    count = 0
                    for index, interview_item in enumerate(interviews):
                        if interview_item == interview.id:
                            count += index + 1
                            break
                    return count

    def get_member_name(self, obj):
        return business_order_public.get_business_order_member_name(obj)

    def get_apply_settlement_time(self, obj):
        if obj.business_settlement:
            return obj.business_settlement.created_at.strftime('%Y-%m-%d %H:%M:%S')

    def get_project_offer_id(self, obj):
        if obj.type == BusinessOrderTypeEnum.enterprise:
            coach_offer = CoachOffer.objects.filter(
                status=CoachOfferStatusEnum.joined, coach=obj.coach, project_offer__project=obj.project,
                deleted=False, project_offer__deleted=False).first()
            if coach_offer:
                return coach_offer.project_offer_id

    def get_contract_url(self, obj):
        return None
        if obj.type == BusinessOrderTypeEnum.personal:
            contract = UserContract.objects.filter(user=obj.coach.user, deleted=False).first()
            if contract:
                return contract.contract_url

    def get_history_record(self, obj):
        return obj.history_record

    def get_platform_scale(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            return f"{obj.platform_scale}%"

    def get_platform_service_amount(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            return str(Decimal(str(obj.platform_service_amount)) / Decimal('100'))

    def get_tax_point(self, obj):
        if obj.tax_point is not None:
            return f"{obj.tax_point}%"

    def get_tax_amount(self, obj):
        if obj.tax_amount is not None:
            return str(Decimal(str(obj.tax_amount)) / Decimal('100'))

    def get_coach_actual_income(self, obj):
        return str(Decimal(str(obj.coach_actual_income)) / Decimal('100'))

    def get_project_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.enterprise:
            if obj.project:
                return obj.project.full_name
            elif obj.extra_info and 'project_name' in obj.extra_info:
                return obj.extra_info['project_name']


    def get_class_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.public_course:
            public_course_coach = business_order_public.get_business_order_to_public_course(obj)
            if public_course_coach:
                return public_course_coach.class_name
            elif obj.extra_info and 'project_name' in obj.extra_info:
                return obj.extra_info['project_name']

    def get_coach_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal:
            return obj.coach.personal_name if obj.coach.personal_name else obj.coach.user.cover_name
        else:
            return obj.coach.user.cover_name

    def get_coach_price(self, obj):
        amount = str(Decimal(obj.coach_price) / Decimal('100'))
        if obj.duration_type == BusinessOrderDurationTypeEnum.hour:
            return f"{amount}元/小时"
        return f"{amount}元/天"

    def get_work_time(self, obj):
        if obj.extra_info and 'work_time' in obj.extra_info:
            return obj.extra_info['work_time']
        return f"{obj.work_start_time.strftime('%Y-%m-%d %H:%M')}-{obj.work_end_time.strftime('%Y-%m-%d %H:%M')}"

    def get_duration(self, obj):
        duration = int(obj.duration) if isinstance(obj.duration, float) and obj.duration.is_integer() else obj.duration
        if obj.duration_type == BusinessOrderDurationTypeEnum.hour:
            return f"{duration}小时"
        return f"{duration}天"

    def get_member_paid(self, obj):
        # 只有个人辅导使用这个字段
        if obj.type == BusinessOrderTypeEnum.personal and obj.work_type == WorkTypeEnum.one_to_one:
            return str(Decimal(str(obj.member_paid)) / Decimal('100'))


def edit_business_order_tax_scale(business_order, data, user):
    if data.get('coach_price'):
        coach_price = data['coach_price']
        coach_price = Decimal(str(coach_price)) * Decimal('100')
        coach_price_type = data['coach_price_type']
        old_price_type = business_order.duration_type
        business_order.duration_type = coach_price_type
        old_text_price = str(Decimal(str(business_order.coach_price)) / Decimal('100'))
        old_text_price_type = "元/小时" if business_order.duration_type == BusinessOrderDurationTypeEnum.hour else "元/天"
        old_text = f"{old_text_price}{old_text_price_type}"
        business_order.coach_price = coach_price

        now_text_price = str(Decimal(str(business_order.coach_price)) / Decimal('100'))
        now_text_price_type = "元/小时" if business_order.duration_type == BusinessOrderDurationTypeEnum.hour else "元/天"
        now_text = f"{now_text_price}{now_text_price_type}"
        content = f"教练单价由{old_text}修改为{now_text}"
        duration = business_order.duration
        if old_price_type != business_order.duration_type:
            price_type_content = f"教练单价单位由{BusinessOrderDurationTypeEnum(old_price_type).describe()}修改为" \
                                 f"{BusinessOrderDurationTypeEnum(business_order.duration_type).describe()}"
            content = content + '/' + price_type_content
            # 修改单位换算时长
            duration = business_order_public.get_business_order_time(
                business_order.work_start_time, business_order.work_end_time, business_order.duration_type)
            business_order.duration = duration

        if business_order.type != BusinessOrderTypeEnum.personal:
            tax_amount, coach_actual_income = business_order_public.calculation_tax_amount(
                coach_price, duration, business_order.tax_point, business_order)
            business_order.tax_amount = tax_amount
            business_order.coach_actual_income = coach_actual_income
            

        price_data = {"record_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                      "user_id": user.id, "true_name": user.cover_name, "content": content,
                      "remark": data.get('remark')}

        if business_order.history_record:
            history_data = business_order.history_record
            history_data['coach_price'].insert(0, price_data)
        else:
            history_data = {"coach_price": [price_data], "tax_point": []}
        business_order.history_record = history_data
        business_order.save()

    elif data.get('tax_point'):
        old_tax_point = business_order.tax_point
        
        if business_order.type == BusinessOrderTypeEnum.personal:
            # 个人辅导的收入计算
            tax_point = data['tax_point']
            # 通过businessorder查询interview
            relation = BusinessOrder2Object.objects.filter(business_order_id=business_order.id, type=WorkTypeEnum.one_to_one).first()
            if relation:
                interview = ProjectInterview.objects.filter(id=relation.object_id).first()
                if interview:
                    platform_service_scale, real_income, tax_amount, platform_service_amount, real_tax_point = coach_public.calculate_coach_income_details(
                        interview, tax_point)
                    business_order.tax_amount = tax_amount
                    business_order.coach_actual_income = real_income
                    business_order.tax_point = tax_point
                    business_order.save()
    
        else:
            tax_point = Decimal(str(tax_point))
            tax_amount, coach_actual_income = business_order_public.calculation_tax_amount(
                business_order.coach_price, business_order.duration, tax_point, business_order)
            business_order.tax_amount = tax_amount
            business_order.coach_actual_income = int(coach_actual_income)
            business_order.tax_point = float(tax_point)
            business_order.save()

        # 记录修改历史
        now_tax_point = business_order.tax_point
        content = f"税点由{old_tax_point}%修改为{now_tax_point}%"
        tax_point_data = {"record_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            "user_id": user.id, "true_name": user.cover_name, "content": content,
                            "remark": data.get('remark')}
        if business_order.history_record:
            history_data = business_order.history_record
            history_data['tax_point'].insert(0, tax_point_data)
        else:
            history_data = {"coach_price": [], "tax_point": [tax_point_data]}
        business_order.history_record = history_data
        business_order.save()
    else:
        pass
    return business_order


def business_order_update_pay_status(business_orders, status):
    with transaction.atomic():
        total_count = business_orders.count()
        success_list, fail_list = [], []
        for business_order in business_orders:
            if business_order.business_settlement_id:
                fail_list.append(str(business_order.id))
            elif status == BusinessOrderPayStatusEnum.paid:
                if business_order.pay_status in [BusinessOrderPayStatusEnum.non_payment, BusinessOrderPayStatusEnum.non]:
                    success_list.append(business_order.id)
                    business_order.pay_status = BusinessOrderPayStatusEnum.paid
                    if not business_order.pay_time:
                        business_order.pay_time = datetime.datetime.now()
                    business_order.save()
                else:
                    fail_list.append(str(business_order.id))
            elif status == BusinessOrderPayStatusEnum.non_payment:
                if business_order.pay_status == BusinessOrderPayStatusEnum.non:
                    success_list.append(business_order.id)
                    business_order.pay_status = BusinessOrderPayStatusEnum.non_payment
                    if not business_order.pay_time:
                        business_order.pay_time = datetime.datetime.now()
                    business_order.save()
                else:
                    fail_list.append(str(business_order.id))
            else:
                fail_list.append(str(business_order.id))
        if len(success_list) == total_count:
            all_success = True
            success_content = None
            fail_content = None
            fail_list_content = None
        else:
            all_success = False
            success_content = f"{len(success_list)}条订单标记{BusinessOrderPayStatusEnum(status).describe()}状态成功！" \
                if success_list else None
            fail_content = f"{len(fail_list)}条订单因不符合条件，标记{BusinessOrderPayStatusEnum(status).describe()}状态失败！" \
                if fail_list else None
            fail_list_content = f"订单编号：{'、'.join(fail_list)}" if fail_list else None

        data = {"all_success": all_success, "success_content": success_content, "fail_content": fail_content,
                "fail_list_content": fail_list_content}
        return data


class BusinessOrderExtraCostBaseSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id')
    project_name = serializers.SerializerMethodField(help_text='项目名称')
    class_name = serializers.SerializerMethodField(help_text='班级名称')
    coach_name = serializers.CharField(source='coach.user.true_name', help_text='教练名称')
    work_time = serializers.SerializerMethodField(help_text='工作时间')
    company_name = serializers.SerializerMethodField(help_text='公司简称')
    extra_cost = serializers.SerializerMethodField(help_text='额外费用')

    def get_work_time(self, obj):
        if obj.extra_info and 'work_time' in obj.extra_info:
            return obj.extra_info['work_time']
        return f"{obj.work_start_time.strftime('%Y-%m-%d %H:%M')}-{obj.work_end_time.strftime('%Y-%m-%d %H:%M')}"

    def get_class_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.public_course:
            public_course_coach = business_order_public.get_business_order_to_public_course(obj)
            if public_course_coach:
                return public_course_coach.class_name
            elif obj.extra_info and 'project_name' in obj.extra_info:
                return obj.extra_info['project_name']

    def get_company_name(self, obj):
        if obj.project:
            return obj.project.company.real_name

    def get_coach_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.personal.value:
            return obj.coach.personal_name if obj.coach.personal_name else obj.coach.user.cover_name
        else:
            return obj.coach.user.cover_name

    def get_extra_cost(self, obj):
        extra_cost = BusinessOrderExtraCost.objects.filter(
            business_order=obj, deleted=False).order_by('marking_date').all()
        data = []
        for item in extra_cost:
            data.append({
                "id": str(item.id),
                "describe": item.describe,
                "marking_date": item.marking_date.strftime('%Y-%m-%d'),
                "cost": item.actual_cost,
                "notes": item.notes
            })
        return data

    def get_project_name(self, obj):
        if obj.type == BusinessOrderTypeEnum.enterprise.value:
            if obj.project:
                return obj.project.name
            elif obj.extra_info and 'project_name' in obj.extra_info:
                return obj.extra_info['project_name']

    class Meta:
        model = BusinessOrder
        fields = ('id', 'project_name', 'class_name', 'coach_name', 'work_time', 'company_name', 'extra_cost')
