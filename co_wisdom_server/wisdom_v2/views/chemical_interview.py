from drf_yasg import openapi
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from django.db.models import Q


from utils.api_response import parameter_error_response, success_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.common import project_coach_public
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum

from wisdom_v2.models_file import ChemicalInterviewModule, ChemicalInterview2Coach
from wisdom_v2.models import ProjectMember, Coach, ProjectInterview, WorkWechatUser, ProjectCoach, PublicAttr
from wisdom_v2.views.chemical_interview_action import ChemicalInterviewModuleSerializer, \
    ChemicalInterviewModuleCreateSerializer, ChemicalInterviewModuleUpdateSerializer, \
    ChemicalInterviewModuleCoachListSerializer, ChemicalInterviewModuleRandomCoachListSerializer, get_random_coach, \
    ChemicalInterview2CoachSerializer
from utils.messagecenter.getui import send_work_wechat_coach_notice
from wisdom_v2.views.constant import ATTR_TYPE_COACH_TASK


class ChemicalInterviewModuleViewSet(viewsets.ModelViewSet):
    queryset = ChemicalInterviewModule.objects.filter(deleted=False)
    serializer_class = ChemicalInterviewModuleSerializer

    @swagger_auto_schema(
        operation_id='后台化学面谈配置详情',
        operation_summary='后台化学面谈配置详情',
        manual_parameters=[
            openapi.Parameter(
                'project_member_id	', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER,
                required=True),
        ],
        tags=['后台化学面谈配置相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def chemical_interview_module_detail(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params['project_member_id']
            project_member = ProjectMember.objects.get(pk=project_member_id)
        except (KeyError, ProjectMember.DoesNotExist):
            return parameter_error_response()
        chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()
        if not chemical_interview_module:
            return success_response()
        serializer = self.get_serializer(chemical_interview_module)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建化学面谈配置',
        operation_summary='创建化学面谈配置',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教练者id'),
                'growth_goals': openapi.Schema(type=openapi.TYPE_NUMBER, description='成长目标数据'),
                'project_interested': openapi.Schema(type=openapi.TYPE_STRING, description='利益相关者数据'),
                'write_condition': openapi.Schema(type=openapi.TYPE_NUMBER, description='填写条件'),
                'remind_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='提醒方式')
            }
        ),
        tags=['后台化学面谈配置相关']
    )
    def create(self, request, *args, **kwargs):
        self.serializer_class = ChemicalInterviewModuleCreateSerializer
        return success_response(super().create(request, *args, **kwargs).data)

    @swagger_auto_schema(
        operation_id='修改化学面谈配置',
        operation_summary='修改化学面谈配置',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='化学面谈id'),
                'max_interview_number': openapi.Schema(type=openapi.TYPE_NUMBER, description='最大面谈次数'),
                'duration': openapi.Schema(type=openapi.TYPE_NUMBER, description='面谈时长'),
                'start_time': openapi.Schema(type=openapi.TYPE_STRING, description='开始日期'),
                'end_time': openapi.Schema(type=openapi.TYPE_STRING, description='结束日期'),
                'coach_source': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练来源'),
                'coach': openapi.Schema(type=openapi.TYPE_ARRAY, description='教练',
                                        items=openapi.Schema(type=openapi.TYPE_NUMBER)),
                'deleted': openapi.Schema(type=openapi.TYPE_NUMBER, description='是否删除')
            }
        ),
        tags=['后台化学面谈配置相关']
    )
    @action(methods=['post'], detail=False, url_path='update', serializer_class=ChemicalInterviewModuleUpdateSerializer)
    def update_chemical_interview_module(self, request, *args, **kwargs):
        try:
            chemical_interview_module_id = request.data.get('id')
            chemical_interview_module = ChemicalInterviewModule.objects.get(pk=chemical_interview_module_id)
        except ChemicalInterviewModule.DoesNotExist:
            return parameter_error_response()
        data = request.data.copy()
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        chemical_interview_module = serializer.update(chemical_interview_module, data)
        serializer = ChemicalInterviewModuleSerializer(chemical_interview_module)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='后台化学面谈配置教练列表',
        operation_summary='后台化学面谈配置教练列表',
        manual_parameters=[
            openapi.Parameter('project_member_id', openapi.IN_QUERY, description='project_member_id',
                              type=openapi.TYPE_STRING),
            openapi.Parameter('coach_type', openapi.IN_QUERY, description='教练类型', type=openapi.TYPE_STRING),
            openapi.Parameter('name', openapi.IN_QUERY, description='教练姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('order_receiving_status', openapi.IN_QUERY, description='接单状态',
                              type=openapi.TYPE_BOOLEAN),
        ],
        tags=['后台化学面谈配置相关']
    )
    @action(methods=['get'], detail=False, url_path='coach_list',
            serializer_class=ChemicalInterviewModuleCoachListSerializer)
    def chemical_interview_module_coach_list(self, request, *args, **kwargs):
        try:
            name = request.query_params.get('name')
            coach_type = request.query_params.get('coach_type')
            order_receiving_status = request.query_params.get('order_receiving_status')
            project_member_id = request.query_params.get('project_member_id')
            project_member = ProjectMember.objects.get(pk=project_member_id)
        except ProjectMember.DoesNotExist:
            return parameter_error_response()
        coach = Coach.objects.filter(
            deleted=False, user_coach__project_id=project_member.project_id,
            user_coach__deleted=False, user_coach__resume__isnull=False).order_by('-created_at')
        if name:
            coach = coach.filter(Q(user__true_name__icontains=name) | Q(user__name__icontains=name))
        if coach_type:
            coach = coach.filter(coach_type__in=coach_type.split(','))
        if order_receiving_status:
            order_receiving_status = True if str(order_receiving_status).lower() == 'true' else False
            coach = coach.filter(order_receiving_status=order_receiving_status)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(coach, self.request)
        serializer = self.get_serializer(page_list, many=True, context={"project_member_id": project_member_id,
                                                                        "user_id": project_member.user_id,
                                                                        "project_id": project_member.project_id})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='后台化学面谈配置随机教练列表',
        operation_summary='后台化学面谈配置随机教练列表',
        manual_parameters=[
            openapi.Parameter('project_member_id', openapi.IN_QUERY, description='project_member_id',
                              type=openapi.TYPE_NUMBER)
        ],
        tags=['后台化学面谈配置相关']
    )
    @action(methods=['get'], detail=False, url_path='random_coach_list',
            serializer_class=ChemicalInterviewModuleRandomCoachListSerializer)
    def chemical_interview_module_random_coach_list(self, request, *args, **kwargs):
        try:
            project_member_id = request.query_params.get('project_member_id')
            project_member = ProjectMember.objects.get(pk=project_member_id)
            max_interview_number = request.query_params['max_interview_number']
        except (ProjectMember.DoesNotExist, KeyError):
            return parameter_error_response()
        coach = get_random_coach(project_member.pk, project_member.project_id, max_interview_number)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(coach, self.request)
        serializer = self.get_serializer(page_list, many=True, context={"user_id": project_member.user_id,
                                                                        "project_id": project_member.project_id})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


class ChemicalInterview2CoachViewSet(viewsets.ModelViewSet):
    queryset = ChemicalInterview2Coach.objects.filter(deleted=False)
    serializer_class = ChemicalInterview2CoachSerializer

    @swagger_auto_schema(
        operation_id='后台化学面谈详情',
        operation_summary='后台化学面谈详情',
        manual_parameters=[
            openapi.Parameter(
                'project_member_id	', openapi.IN_QUERY, description='被教练者id', type=openapi.TYPE_NUMBER,
                required=True),
        ],
        tags=['后台化学面谈相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def chemical_interview_detail(self, request, *args, **kwargs):
        try:
            interview_id = request.query_params.get('interview_id')
            chemical_interview_id = request.query_params.get('chemical_interview_id')
            if interview_id:
                project_interview = ProjectInterview.objects.get(id=interview_id)
                chemical_interview = project_interview.chemical_interview.filter(deleted=False).first()
            else:
                chemical_interview = ChemicalInterview2Coach.objects.get(id=chemical_interview_id)
        except (ProjectInterview.DoesNotExist, ChemicalInterview2Coach.DoesNotExist):
            return parameter_error_response()
        serializer = self.get_serializer(chemical_interview)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改后台化学面谈',
        operation_summary='修改后台化学面谈',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='化学面谈id'),
                'chemical_interview_status': openapi.Schema(type=openapi.TYPE_NUMBER, description='面谈状态'),
            }
        ),
        tags=['后台化学面谈相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_chemical_interview(self, request, *args, **kwargs):
        try:
            chemical_interview_id = request.data['id']
            chemical_interview_status = request.data['chemical_interview_status']
            chemical_interview = ChemicalInterview2Coach.objects.get(pk=chemical_interview_id)
        except (KeyError, ChemicalInterview2Coach.DoesNotExist):
            return parameter_error_response()
        if chemical_interview_status == ChemicalInterviewStatusEnum.selected:
            exists_obj = ChemicalInterview2Coach.objects.filter(
                    chemical_interview_module=chemical_interview.chemical_interview_module,
                    chemical_interview_status=ChemicalInterviewStatusEnum.selected, deleted=False
            ).exclude(id=chemical_interview_id).first()
            if exists_obj:
                return parameter_error_response(f'修改失败，请先将和{exists_obj.coach.user.cover_name}教练的结果修改为不选择，'
                                                f'再将该教练设置为选择')
            chemical_interview.chemical_interview_status = chemical_interview_status
            project_coach_public.user_add_coach(
                chemical_interview.interview.public_attr.target_user_id,
                chemical_interview.interview.public_attr.project_id,
                chemical_interview.coach_id, chemical_interview.coach.user_id)
            chemical_interview.save()
        else:
            chemical_interview.chemical_interview_status = ChemicalInterviewStatusEnum.unselected
            chemical_interview.save()
            project_coach_public.member_user_unbind_coach(
                chemical_interview.interview.public_attr.target_user_id,
                chemical_interview.interview.public_attr.project_id,
                chemical_interview.coach_id, chemical_interview.coach.user_id)

        work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False,
            user_id=chemical_interview.interview.public_attr.user_id,
            deleted=False
        ).first()
        if work_wechat_user:
            user = work_wechat_user.user
            # 获取当前请求的用户作为发送者
            sender_id = request.user.id if request.user and hasattr(request.user, 'id') else None
            send_work_wechat_coach_notice.delay(
                work_wechat_user.wx_user_id,
                'chemical_interview_pass' if
                chemical_interview_status == ChemicalInterviewStatusEnum.selected
                else 'chemical_interview_rejected',
                interview_id=chemical_interview.interview.pk,
                project_name=chemical_interview.interview.public_attr.project.full_name,
                coachee_name=chemical_interview.interview.public_attr.target_user.cover_name,
                user_id=user.pk,
                coachee_id=chemical_interview.interview.public_attr.target_user_id,
                coach_name=user.cover_name,
                coach_id=work_wechat_user.user_id,
                project_id=chemical_interview.interview.public_attr.project_id,
                sender_id=sender_id
            )
        serializer = self.get_serializer(chemical_interview)
        return success_response(serializer.data)
