import datetime

from django.db.models import Q
from rest_framework import serializers
from django.db import transaction

from utils.message.lark_message import LarkMessageCenter
from utils.api_response import WisdomValidationError
from utils import validate
from wisdom_v2.common import stakeholder_interview_public, project_interested_public, company_member_public
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum
from wisdom_v2.enum.service_content_enum import NoticeTemplateTypeEnum, NoticeChannelTypeEnum
from wisdom_v2.models_file import StakeholderInterview, StakeholderInterviewModule
from wisdom_v2.models import UserNoticeRecord, ProjectInterested, CompanyMember, User, Coach


class AppStakeholderInterviewDetailSerializer(serializers.ModelSerializer):
    status = serializers.SerializerMethodField(help_text='邀请状态 1-可预约 2-已失效')
    msg = serializers.SerializerMethodField(help_text='详情信息')

    class Meta:
        model = StakeholderInterview
        fields = ('status', 'msg')

    def get_status(self, obj):
        if obj.deleted:
            return 2  # 已失效
        else:
            if obj.interview_id:
                return 3  # 已预约
            else:
                return 1  # 可预约

    def get_msg(self, obj):
        if not obj.deleted:
            stakeholder_interview_module = obj.stakeholder_interview_module

            if stakeholder_interview_module.coach_task_id:
                coach = Coach.objects.filter(
                    user_id=stakeholder_interview_module.coach_task.public_attr.user_id, deleted=False).first()
            else:
                coach = None
            data = {
                "coach_user_id": coach.user_id if coach else None,
                "stakeholder_user_id": obj.project_interested.interested_id,
                "customer_name": stakeholder_interview_module.project_member.user.cover_name,
                "start_date": stakeholder_interview_module.start_date,
                "end_date": stakeholder_interview_module.end_date,
                "coach_name": coach.user.cover_name if coach else None,
                "stakeholder_interview_id": obj.id,
                "duration": stakeholder_interview_module.duration,
                "interview_id": obj.interview_id
            }
            return data


class AppStakeholderInterviewSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='利益相关者访谈id')
    status = serializers.SerializerMethodField(help_text='状态')
    true_name = serializers.CharField(source='project_interested.interested.cover_name')
    relation = serializers.IntegerField(source='project_interested.relation')
    position = serializers.SerializerMethodField(help_text='职位')

    class Meta:
        model = StakeholderInterview
        fields = ('id', 'status', 'true_name', 'relation', 'position')

    def get_status(self, obj):
        if not obj.is_send_message:
            return 1  # 未发送
        else:
            if obj.interview_id:
                return 3  # 已预约
            else:
                return 2  # 已发送

    def get_position(self, obj):
        company_member = CompanyMember.objects.filter(company=obj.project_interested.project.company,
                                                      user=obj.project_interested.interested,
                                                      deleted=False).first()
        if company_member:
            return company_member.position


class StakeholderListSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(help_text='利益相关id')
    true_name = serializers.CharField(source='interested.cover_name', help_text='利益相关者姓名')
    relation = serializers.IntegerField(help_text='上下级关系 1:上级 2：平级 3:下级')
    position = serializers.SerializerMethodField(help_text='利益相关者职位')
    is_set = serializers.SerializerMethodField(help_text='是否已配置利益相关者访谈')
    is_cancel = serializers.SerializerMethodField(help_text='是否可以取消勾选')

    class Meta:
        model = ProjectInterested
        fields = ('id', 'true_name', 'relation', 'position', 'is_set', 'is_cancel')

    def get_position(self, obj):
        company_member = CompanyMember.objects.filter(company=obj.project.company, user=obj.interested,
                                                      deleted=False).first()
        if company_member:
            return company_member.position

    def get_is_set(self, obj):
        project_member = self.context.get('project_member')
        if project_member:
            return stakeholder_interview_public.get_stakeholder_interview_is_set(project_member, obj)
        return False

    def get_is_cancel(self, obj):
        project_member = self.context.get('project_member')
        if project_member:
            return stakeholder_interview_public.get_stakeholder_interview_is_cancel(project_member, obj)
        return True


class AppStakeholderInterviewModuleSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='利益相关者访谈配置id')
    stakeholder_interview_number = serializers.IntegerField(help_text='访谈人数')
    duration = serializers.SerializerMethodField(help_text='访谈时长 默认0.5，支持设置为0.5、1、1.5、2，4个值')
    send_message_time = serializers.SerializerMethodField(help_text='利益相关者访谈提醒时间')
    stakeholder = serializers.SerializerMethodField(help_text='利益相关者列表')
    status = serializers.SerializerMethodField(help_text='利益相关者访谈状态')
    coach_task_id = serializers.IntegerField(help_text='教练任务id')

    class Meta:
        model = StakeholderInterviewModule
        fields = ('id', 'stakeholder_interview_number', 'duration', 'stakeholder', 'send_message_time', 'status',
                  'coach_task_id')

    def get_status(self, obj):
        if obj.coach_task.coach_submit_time:  # 已提交
            return 3  # 查看报告
        else:
            if datetime.datetime.now().date() > obj.end_date:  # 已经过期
                return 2  # 等待报告生成
        return 1

    def get_duration(self, obj):
        return int(round(obj.duration * 60, 0))

    def get_send_message_time(self, obj):
        user_notice_record = UserNoticeRecord.objects.filter(
            user=obj.project_member.user, project=obj.project_member.project, channel=NoticeChannelTypeEnum.email,
            type=NoticeTemplateTypeEnum.coachee_invite_stakeholder_interview_reservation, deleted=False).last()
        if user_notice_record:
            send_message_time = user_notice_record.created_at + datetime.timedelta(days=1)
            return send_message_time.strftime('%m月%d日 %H:%M')

    def get_stakeholder(self, obj):
        data = []
        project_member = obj.project_member
        if project_member:
            stakeholders = ProjectInterested.objects.filter(
                master_id=project_member.user_id, project_id=project_member.project_id,
                deleted=False).order_by('-created_at')
            if stakeholders.exists():
                data = StakeholderListSerializer(stakeholders, many=True,
                                                 context={'project_member': project_member}).data
        return data


class AppStakeholderInterviewModuleUpdateSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='利益相关者访谈配置id', write_only=True, required=True)
    stakeholder = serializers.ListField(help_text='利益相关关系id列表', write_only=True, required=False)

    class Meta:
        model = StakeholderInterviewModule
        fields = ['id', 'stakeholder']

    def update(self, instance, validated_data):
        validated_data.pop('id')
        deleted_list, add_list = [], []
        if 'stakeholder' in validated_data.keys():
            stakeholder = validated_data.pop('stakeholder')
            if instance.stakeholder_interview_number > len(stakeholder):
                raise WisdomValidationError(f"本次访谈需要邀请{instance.stakeholder_interview_number}位同事，"
                                            f"您还要再选择{instance.stakeholder_interview_number-len(stakeholder)}位参与同事")
            if instance.stakeholder_interview_number < len(stakeholder):
                raise WisdomValidationError(f"本次访谈仅需要邀请{instance.stakeholder_interview_number}位同事参与")
            err, deleted_list, add_list = stakeholder_interview_public.check_update_stakeholder(stakeholder, instance)
            if err:
                raise WisdomValidationError(err)
        with transaction.atomic():
            stakeholder_interview_public.update_stakeholder_interview(add_list, deleted_list, instance)

        params = {'project_name': f'{instance.project_member.project.company.real_name}-{instance.project_member.project.name}',
                  'user_name': instance.project_member.user.cover_name}
        LarkMessageCenter().send_business_message.delay(params, LarkMessageTypeEnum.stakeholder_interview_personnel_selection.value,
                                                        project_id=instance.project_member.project.id)
        return instance


def member_add_stakeholder(true_name, phone, email, position, relation, project_member):
    err = None
    project_interested = None

    if not validate.validate_phone(phone):
        err = '请输入正确的手机号'
        return err, project_interested
    if not validate.validate_email(email):
        err = '请输入正确的邮箱地址'
        return err, project_interested
    # 通过手机号查
    phone_company_member = CompanyMember.objects.filter(user__phone=phone, company=project_member.project.company,
                                                        deleted=False)
    if phone_company_member.exists():
        phone_company_member = phone_company_member.first()
        if phone_company_member.user.phone == project_member.user.phone:
            err = '不能添加自己为利益相关者'
            return err, project_interested
        err, project_interested = project_interested_public.add_project_interested(project_member, phone_company_member, relation, position,
                                                               exists_company_member=True)
        return err, project_interested
    # 通过邮箱查
    email_company_member = CompanyMember.objects.filter(user__email=email, company=project_member.project.company,
                                                        deleted=False)
    if email_company_member.exists():
        email_company_member = email_company_member.first()
        if email_company_member.user.email == project_member.user.email:
            err = '不能添加自己为利益相关者'
            return err
        err, project_interested = project_interested_public.add_project_interested(project_member, email_company_member, relation, position,
                                                               exists_company_member=True)
        return err, project_interested

    # 邮箱和手机号没有查到，新建user,company_member
    exists_phone_user = User.objects.filter(Q(phone=phone) | Q(name=phone), deleted=False)
    if exists_phone_user.exists():
        err = '添加失败，手机号已存在'
        return err, project_interested
    exists_email_user = User.objects.filter(email=email)
    if exists_email_user.exists():
        err = '添加失败，邮箱已存在'
        return err, project_interested

    # 创建user、company_member、project_interested
    company_member = company_member_public.create_company_member(true_name, phone, email, position, project_member)
    err, project_interested = project_interested_public.add_project_interested(project_member, company_member, relation, position)
    if err:
        return err, project_interested
    return err, project_interested
