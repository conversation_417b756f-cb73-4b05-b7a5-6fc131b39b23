
from celery import shared_task
import pendulum
from django.conf import settings

from utils import aliyun, excel_pubilc, randomPassword
from wisdom_v2.common import project_service_public
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewCoachSourceEnum, ChemicalInterviewStatusEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewTypeEnum, ProjectInterviewPlaceCategoryEnum
from wisdom_v2.models import Coach, InterviewRecordTemplateAnswer, Project, ProjectCoach, WorkWechatUser, ProjectMember
from wisdom_v2.models_file import ChemicalInterview2Coach
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum
from utils.message.lark_message import LarkMessageCenter
from utils.messagecenter.getui import send_work_wechat_coach_notice, send_work_wechat_coachee_notice
from utils.wechat_oauth import WeChatMiniProgram
from utils.qr_code import get_base_qr_code
from utils.message.email import message_send_email_base
from utils.send_account_email import get_project_manage_wx_user_id

def get_chemical_interview(coach_users, coachee_users, project_id, start_date, end_date):
    """
    获取指定项目下化学访谈
    可选参数：教练用户列表，被教练者用户列表，开始日期，结束日期
    :param coach_users: 教练用户列表
    :param coachee_users: 被教练者用户列表
    :param project_id: 项目id
    :param start_date: 开始日期 type: date
    :param end_date: 结束日期 type: date
    :return: 化学面谈列表
    """
    queryset = ChemicalInterview2Coach.objects.filter(
        interview__type=ProjectInterviewTypeEnum.chemical_interview.value,
        interview__place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
        interview__public_attr__project_id=project_id,
        chemical_interview_module__deleted=False,
        interview__deleted=False,
        deleted=False,
    ).exclude(interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('interview__public_attr__start_time')
    if start_date:
        queryset = queryset.filter(interview__public_attr__start_time__date__gte=start_date)
    if end_date:
        queryset = queryset.filter(interview__public_attr__end_time__date__lte=end_date)
    if coach_users:
        queryset = queryset.filter(interview__public_attr__user__in=coach_users)
    if coachee_users:
        queryset = queryset.filter(chemical_interview_module__project_member__user__in=coachee_users)

    return queryset


def download_chemical_interview(project_id):
    project = Project.objects.filter(id=project_id, deleted=False).first()
    project_name = project.name if project else ''
    company_name = project.company.real_name if project.company else ''

    all_chemical_interview = ChemicalInterview2Coach.objects.filter(
        interview__public_attr__project_id=project_id, deleted=False, interview__deleted=False,
        interview__type=ProjectInterviewTypeEnum.chemical_interview.value).exclude(
        interview__public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).all()

    data = [['客户姓名', '教练姓名', '化学面谈时间', '面谈状态', '化学面谈记录']]

    for item in all_chemical_interview:
        interview = item.interview
        tmp_answer = InterviewRecordTemplateAnswer.objects.filter(interview=interview, deleted=False).first()
        interview_time = f'{interview.public_attr.start_time.strftime("%Y.%m.%d %H:%M")}~{interview.public_attr.end_time.strftime("%H:%M")}'

        coach_name = interview.public_attr.user.cover_name
        coachee_name = interview.public_attr.target_user.cover_name

        chemical_interview_status = ChemicalInterviewStatusEnum.get_display(item.chemical_interview_status)
        item_data = [coachee_name, coach_name, interview_time, chemical_interview_status]
        if tmp_answer:
            # 客户个性特征及面临的挑战
            focus_topic_answer = project_service_public.get_chemical_interview_coach_question(interview, 5)
            if focus_topic_answer:
                focus_topic = focus_topic_answer.answer
            else:
                focus_topic = ''
            # 教练针对客户的初步策略
            strategy_answer = project_service_public.get_chemical_interview_coach_question(interview, 6)
            if strategy_answer:
                strategy = strategy_answer.answer
            else:
                strategy = ''
            # 客户对即将进行的项目的态度
            customer_readiness_answer = project_service_public.get_chemical_interview_coach_question(interview, 2)
            if customer_readiness_answer:
                customer_readiness_title = customer_readiness_answer.option.title
                customer_readiness_option_custom = customer_readiness_answer.option_custom
                customer_readiness = f'{customer_readiness_title}，{customer_readiness_option_custom}'
            else:
                customer_readiness = ''
            # 化学面谈效果评价
            appraise_answer = project_service_public.get_chemical_interview_coach_question(interview, 4)
            if appraise_answer:
                appraise_title = appraise_answer.option.title
                appraise_user_option_custom = appraise_answer.option_custom
                appraise = f'{appraise_title}，{appraise_user_option_custom}'
            else:
                appraise = ''
            interview_str = f"""{coach_name}教练和{coachee_name}本次的化学面谈的效果{appraise}。在整个过程中，{coachee_name}展现出了对于教练项目的{customer_readiness}。教练通过沟通发现，客户目前的主要挑战是：{focus_topic}。基于这些挑战，教练对于未来教练方向的建议是：{strategy}"""
        else:
            interview_str = '暂无'
        item_data += [interview_str]
        data.append(item_data)

    #  文件名
    name = f'admin/project_chemical_interview/{pendulum.now().strftime("%Y%m%d%H%M%S")}/{company_name}-{project_name}项目化学面谈记录.xlsx'
    # 获取文件数据
    file = excel_pubilc.save_chemical_interview_excel(data, 'chemical_interview')
    # 上传阿里云
    aliyun.AliYun('cwcoach').send_file(name, file.getvalue())

    return f'{settings.ALIYUN_SDN_BASE_URL}/{name}'


# 客户反馈面谈结果后通知管理员，教练和客户
@shared_task(queue='task', ignore_result=True, soft_time_limit=30)
def feedback_notification(chemical_interview_id):
    chemical_interview = ChemicalInterview2Coach.objects.get(pk=chemical_interview_id)
    chemical_interview_status = chemical_interview.chemical_interview_status
    work_wechat_user = WorkWechatUser.objects.filter(
            wx_user_id__isnull=False,
            user_id=chemical_interview.interview.public_attr.user_id,
            deleted=False
        ).first()
    if work_wechat_user:
        user = work_wechat_user.user
        send_work_wechat_coach_notice(
            work_wechat_user.wx_user_id,
            'chemical_interview_pass' if chemical_interview_status == ChemicalInterviewStatusEnum.selected else 'chemical_interview_rejected',
            interview_id=chemical_interview.interview.pk,
            project_name=chemical_interview.interview.public_attr.project.full_name,
            project_id=chemical_interview.interview.public_attr.project_id,
            coachee_name=chemical_interview.interview.public_attr.target_user.cover_name,
            user_id=user.pk,
            coachee_id=chemical_interview.interview.public_attr.target_user_id,
            coach_name=user.cover_name,
            coach_id=work_wechat_user.user_id
        )
    data = {"project_name": chemical_interview.interview.public_attr.project.name,
            "coachee_name": chemical_interview.interview.public_attr.target_user.cover_name,
            "coach_name": chemical_interview.interview.public_attr.user.cover_name}
    if chemical_interview_status == ChemicalInterviewStatusEnum.selected:
        LarkMessageCenter().send_business_message(
            data,
            LarkMessageTypeEnum.chemical_interview_select_coach,
            project_id=chemical_interview.interview.public_attr.project_id
        )
        # 给没有被客户预约过的教练发送通知
        send_coach_result(chemical_interview_id)
        # 给客户发送小程序消息，提醒预约辅导
        interview_message_id = settings.WELCOME_TEMPLATE
        WeChatMiniProgram().template_send(interview_message_id, chemical_interview.interview.public_attr.target_user.openid,
                                           value_list={
                                               'thing3': {'value': f'你有一位教练可预约，快来预约辅导吧！'},
                                               'thing4': {'value': '教练辅导'}},
                                           project_id=chemical_interview.interview.public_attr.project_id,
                                           receiver_id=chemical_interview.interview.public_attr.target_user_id,
                                           template_name='化学面谈完成通知')
    else:
        LarkMessageCenter().send_business_message(
            data,
            LarkMessageTypeEnum.chemical_interview_unselect_coach,
            project_id=chemical_interview.interview.public_attr.project_id
        )
        # 判断是否为最后一个不选择的教练
        if ChemicalInterview2Coach.objects.filter(
                chemical_interview_module=chemical_interview.chemical_interview_module, deleted=False,
                chemical_interview_status=ChemicalInterviewStatusEnum.unselected).count() == \
                chemical_interview.chemical_interview_module.max_interview_number:
            LarkMessageCenter().send_business_message(
                data,
                LarkMessageTypeEnum.chemical_interview_fail,
                project_id=chemical_interview.interview.public_attr.project_id
            )
        # 未选择给客户发消息
        send_coachee_unselect_message(chemical_interview_id)
        update_coach_offer_status(chemical_interview_id)


def update_coach_offer_status(chemical_interview_id):
    chemical_interview = ChemicalInterview2Coach.objects.get(id=chemical_interview_id)
    project = chemical_interview.interview.public_attr.project
    coach = chemical_interview.coach

    # Check if all customers have selected their coaches
    all_interviews = ChemicalInterview2Coach.objects.filter(
        chemical_interview_module__project_member__project=project,
        coach=coach,
        deleted=False
    )

    all_customer_done = True
    for interview in all_interviews:
        module = interview.chemical_interview_module
        selected = ChemicalInterview2Coach.objects.filter(
            chemical_interview_module=module,
            chemical_interview_status=ChemicalInterviewStatusEnum.selected,
            deleted=False
        ).exists()
        if not selected:
            all_customer_done = False
            break

    # Check if this coach was not selected by any customer
    coach_not_selected = not ChemicalInterview2Coach.objects.filter(
        chemical_interview_module__project_member__project=project,
        coach=coach,
        chemical_interview_status=ChemicalInterviewStatusEnum.selected,
        deleted=False
    ).exists()

    if all_customer_done and coach_not_selected:
        # Mark the coach's offer as not selected
        ProjectCoach.objects.filter(project_id=project.id, coach_id=coach.id, deleted=False).update(deleted=True)




# 客户不选择教练后，如果还有面谈次数，则发送邮件和企业微信通知
def send_coachee_unselect_message(chemical_interview_id):
    chemical_interview = ChemicalInterview2Coach.objects.get(id=chemical_interview_id)
    interview = chemical_interview.interview
    chemical_interview_module = chemical_interview.chemical_interview_module
    count = chemical_interview_module.max_interview_number - chemical_interview_module.coaches.filter(
        deleted=False, interview__isnull=False).count()
    if count > 0:
        # 发邮件
        state, content = WeChatMiniProgram().get_miniapp_qrcode(
            scene="refer=2",
            page='pages/index/index')

        if state:
            url_state, short_link = WeChatMiniProgram().get_url_link(
                'pages/index/index',
                f"refer=2")

            url = get_base_qr_code(content, 'coach_resume')
            message_type = 'chemical_interview_result'
            project_member = ProjectMember.objects.filter(
                user=interview.public_attr.target_user,
                project=interview.public_attr.project, deleted=False).first()
            manage_list = project_member.project.manager_list

            params = {
                "true_name": project_member.user.cover_name,
                "count": count,
                "manager_name": manage_list[0]['true_name'] if manage_list else '',
                "manager_email": manage_list[0]['email'] if manage_list else '',
                "manager_phone": manage_list[0]['phone'] if manage_list else '',
                "qr_code_url": url,
                "short_link": short_link,
            }
            email = [project_member.user.email]
            message_send_email_base(message_type=message_type, params=params, to_email=email, project_id=project_member.project_id,
                                   receiver_ids=project_member.user_id)
        # 发企业微信
        text = f'您在本项目中还有{count}次化学面谈机会，请在剩余的候选教练中选择一位您感兴趣的教练进行化学面谈。'
        page = "pages/index/index?refer=2"
        title = "预约化学面谈"
        msg, sender, external_user_id = get_project_manage_wx_user_id(
            interview.public_attr.project.pk,
            interview.public_attr.target_user_id,
            'send_coachee_unselect_message'
        )
        if msg:
            sender_user = WorkWechatUser.objects.filter(
                wx_user_id=sender,
                user__isnull=False,
                deleted=False
            ).first()
            if sender_user:
                send_work_wechat_coachee_notice(
                    sender, text, external_user_id, page, title,
                    content_type='add_msg_template',
                    project_id=interview.public_attr.project.id,
                    project_name=interview.public_attr.project.name,
                    coachee_id=interview.public_attr.target_user_id,
                    coachee_name=interview.public_attr.target_user.cover_name,
                    forward_by=sender_user.user.cover_name,
                    forward_by_id=sender_user.user.id,
                )


# 给教练发送化学面谈结束通知
def send_coach_result(chemical_interview_id):
    '''
    1. 查找本次化学面谈客户配置的所有教练
    2. 遍历每个教练，其所有客户都选择了教练，并且没有被客户预约过
    3. 向教练发送企业微信通知
    '''
    # 获取当前化学面谈
    chemical_interview = ChemicalInterview2Coach.objects.get(id=chemical_interview_id)
    chemical_interview_module = chemical_interview.chemical_interview_module
    # 自主选择教练，不用通知其他教练
    if chemical_interview_module.coach_source == ChemicalInterviewCoachSourceEnum.auto_select.value:
        return

    # 获取该客户配置的其他教练
    all_coach_ids = ChemicalInterview2Coach.objects.filter(
        chemical_interview_module=chemical_interview_module,
        deleted=False
    ).exclude(chemical_interview_status=ChemicalInterviewStatusEnum.selected).values_list('coach_id', flat=True)

    # 遍历每个教练
    for coach_id in all_coach_ids:
        # 获取当前项目
        project = chemical_interview_module.project_member.project

        # 查找当前项目下，这位教练的所有ChemicalInterview2Coach
        all_interviews = ChemicalInterview2Coach.objects.filter(
            chemical_interview_module__project_member__project=project,
            coach_id=coach_id,
            deleted=False,
        )

        # 检查all_interviews中的interview是否都为空
        all_interviews_empty = all(
            interview.interview is None
            for interview in all_interviews
        )

        # 不是空，说明教练被预约过，不用发送通知
        if not all_interviews_empty:
            return

        # 查找每位客户是否选择好了教练
        all_customer_done = True
        for interview in all_interviews.all():
            module = interview.chemical_interview_module
            selected = ChemicalInterview2Coach.objects.filter(chemical_interview_module=module,
                                                    chemical_interview_status=ChemicalInterviewStatusEnum.selected,
                                                    deleted=False).exists()
            if not selected:
                all_customer_done = False

        if all_customer_done:
            # 向教练发送企业微信通知
            coach = Coach.objects.filter(pk=coach_id, deleted=False).first()
            if coach:
                work_wechat_user = WorkWechatUser.objects.filter(
                    wx_user_id__isnull=False,
                    user_id=coach.user.id,
                    deleted=False
                ).first()

                if work_wechat_user:
                    send_work_wechat_coach_notice(
                        work_wechat_user.wx_user_id,
                        'chemical_interview_all_unselected',
                        project_name=project.full_name,
                        project_id=project.id
                    )

                # 更新教练offer的is_show_in_project_list
                ProjectCoach.objects.filter(project_id=project.id, coach_id=coach.id, deleted=False).update(deleted=True)


def pending_interview_appointment(coach, project_id):
    """
    判断是否还有未预约的化学面谈

    Args:
        coach: 教练对象
        project_id: 项目ID

    Returns:
        bool: True表示有未预约的面谈，False表示没有未预约的面谈
    """
    # 获取该教练在项目中的所有化学面谈关联记录
    chemical_interviews = ChemicalInterview2Coach.objects.filter(
        chemical_interview_module__project_member__project_id=project_id,
        coach=coach,
        deleted=False
    )

    for chemical_interview in chemical_interviews:
        # 如果interview为空,说明还未预约
        if not chemical_interview.interview:
            # 获取该化学面谈模块
            module = chemical_interview.chemical_interview_module

            # 检查该模块是否已有其他教练被选中
            selected_coach = ChemicalInterview2Coach.objects.filter(
                chemical_interview_module=module,
                chemical_interview_status=ChemicalInterviewStatusEnum.selected,
                deleted=False
            ).exists()

            # 如果没有其他教练被选中,说明确实存在未预约的面谈
            if not selected_coach:
                return True

    return False