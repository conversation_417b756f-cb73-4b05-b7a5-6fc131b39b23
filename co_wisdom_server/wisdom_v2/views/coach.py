import re
import datetime

from django.conf import settings
from drf_yasg import openapi
from django.db import transaction
from django.db.models import Q
from datetime import datetime
from drf_yasg.utils import swagger_auto_schema

from wisdom_v2.common import notify_public
from utils.message.email import message_send_email_base
from ..common import activity_public, activity_interview_public
from ..enum.project_enum import ProjectStatusEnum
from ..models import ProjectInterview, Project, Coach, User, Resume, ProjectCoach, ProjectMember, \
    WorkWechatUser, PersonalUser
from .coach_actions import CoachSerializers, CoachProjectSerializer, InterviewRecordSerializer, \
    CoachProjectMemberSerializer, CoachEditSerializer
from rest_framework import viewsets

from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from rest_framework.decorators import action
from utils.trainee_coaches_data import get_all_coaches
from utils import randomPassword, aesencrypt, validate
from utils.queryset import multiple_field_distinct
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum
from wisdom_v2.enum.user_enum import CoachUserTypeEnum
from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL, INTERVIEW_TYPE_COACHING
from wisdom_v2 import utils
from utils.task import refresh_coach_list_data
from django.core.cache import cache

from ..models_file import ActivityCoach


class CoachViewSet(viewsets.ModelViewSet):
    queryset = Coach.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = CoachSerializers

    @swagger_auto_schema(
        operation_id='教练列表',
        operation_summary='教练列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('bind_project_id', openapi.IN_QUERY, description='回显绑定项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('name', openapi.IN_QUERY, description='教练姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_type', openapi.IN_QUERY, description='教练类型', type=openapi.TYPE_STRING),
            openapi.Parameter('coach_auth', openapi.IN_QUERY, description='教练资质', type=openapi.TYPE_STRING),
            openapi.Parameter('email', openapi.IN_QUERY, description='邮箱', type=openapi.TYPE_STRING),
            openapi.Parameter('order_receiving_status', openapi.IN_QUERY, description='接单状态', type=openapi.TYPE_BOOLEAN),
            openapi.Parameter('personal_name', openapi.IN_QUERY, description='个人客户可见姓名', type=openapi.TYPE_STRING),
        ],
        tags=['签约教练相关']
    )
    def list(self, request, *args, **kwargs):
        coach = self.get_queryset()
        name = request.query_params.get('name')
        email = request.query_params.get('email')
        coach_type = request.query_params.get('coach_type')
        phone = request.query_params.get('phone')
        order_receiving_status = request.query_params.get('order_receiving_status')
        coach_auth = request.query_params.get('coach_auth')
        sort = request.query_params.get('sort')
        order = request.query_params.get('order')
        project_id = request.query_params.get('project_id')
        bind_project_id = request.query_params.get('bind_project_id')
        personal_name = request.query_params.get('personal_name')
        coach_industry = request.query_params.get('coach_industry')
        serverd_types = request.query_params.get('serverd_types')
        customer_level = request.query_params.get('customer_level')

        if name:
            coach = coach.filter(Q(user__true_name__icontains=name) | Q(user__name__icontains=name) | Q(personal_name__icontains=name) | Q(user__phone__icontains=name))
        if phone:
            coach = coach.filter(user__phone__icontains=phone)
        if email:
            coach = coach.filter(user__email__icontains=email)
        if coach_type:
            coach = coach.filter(coach_type__in=coach_type.split(','))
        if order_receiving_status:
            order_receiving_status = True if str(order_receiving_status).lower() == 'true' else False
            coach = coach.filter(order_receiving_status=order_receiving_status)
        if coach_auth:
            coach = coach.filter(resumes__coach_auth__in=coach_auth.split(','), resumes__is_customization=False).distinct()
        if project_id:
            coach = coach.filter(user_coach__project_id=project_id, user_coach__deleted=False, user_coach__resume__isnull=False)
        if personal_name:
            coach = coach.filter(personal_name__icontains=personal_name)
        if coach_industry:
            coach = coach.filter(resumes__work_industry__contains=coach_industry.split(','), resumes__is_customization=False).distinct()
        if serverd_types:
            coach = coach.filter(resumes__coach_enterprise_attributes__contains=serverd_types.split(','), resumes__is_customization=False).distinct()
        if customer_level:
            coach = coach.filter(resumes__coach_customer_level__contains=customer_level.split(','), resumes__is_customization=False).distinct()

        if bind_project_id:
            coach = coach.exclude(coach_type=CoachUserTypeEnum.student)
        if sort == 'created_at':
            coach = coach.order_by('created_at') if order == 'ascending' else coach.order_by('-created_at')
        # 排序
        if sort in ['project_count', 'project_member_count', 'interview_record_count', 'target_progress_score',
                    'harvest_score', 'satisfaction_score', 'recommend_score']:
            data = cache.get(f'coach_list_{sort}')
            if data:
                tmp_coaches = data
                refresh_coach_list_data.delay(coach, sort)
            else:
                if sort == 'project_count':
                    tmp_coaches = [[CoachSerializers().get_project_count(tmp_coach), tmp_coach] for tmp_coach in coach]
                elif sort == 'project_member_count':
                    tmp_coaches = [[CoachSerializers().get_project_member_count(tmp_coach), tmp_coach] for tmp_coach in coach]
                elif sort == 'interview_record_count':
                    tmp_coaches = [[CoachSerializers().get_interview_record_count(tmp_coach), tmp_coach] for tmp_coach in coach]
                elif sort == 'target_progress_score':
                    tmp_coaches = [[CoachSerializers().get_target_progress_score(tmp_coach), tmp_coach] for tmp_coach in coach]
                elif sort == 'harvest_score':
                    tmp_coaches = [[CoachSerializers().get_harvest_score(tmp_coach), tmp_coach] for tmp_coach in coach]
                elif sort == 'satisfaction_score':
                    tmp_coaches = [[CoachSerializers().get_satisfaction_score(tmp_coach), tmp_coach] for tmp_coach in coach]
                elif sort == 'recommend_score':
                    tmp_coaches = [[CoachSerializers().get_recommend_score(tmp_coach), tmp_coach] for tmp_coach in coach]
                else:
                    return parameter_error_response('排序参数错误')
                cache.set(f'coach_list_{sort}', tmp_coaches, 300)
            tmp_coaches = sorted(tmp_coaches, key=lambda k: k[0]) if order == 'ascending' \
                else sorted(tmp_coaches, key=lambda k: k[0], reverse=True)
            coach = [tmp_coach[1] for tmp_coach in tmp_coaches]

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(coach, self.request)
        serializer = self.get_serializer(page_list, many=True, context={
            "project_id": project_id,
            "bind_project_id": bind_project_id,
            "user_id": request.user.id
        })
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='教练详情',
        operation_summary='教练详情',
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_QUERY, description='教练id', type=openapi.TYPE_STRING),
        ],
        tags=['签约教练相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def coach_detail(self, request, *args, **kwargs):
        try:
            coach_id = request.query_params.get('id', None)
            coach = Coach.objects.get(pk=coach_id, deleted=False)
        except:
            return parameter_error_response()
        serializer = self.get_serializer(coach)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建教练信息',
        operation_summary='创建教练信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='姓名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='用户手机号'),
                'work_wechat_user_id': openapi.Schema(type=openapi.TYPE_STRING, description='企业微信用户id'),
                'is_add_work_wechat_user': openapi.Schema(type=openapi.TYPE_STRING, description='是否是新建企业微信账号')
            }
        ),
        tags=['签约教练相关']
    )
    def create(self, request, *args, **kwargs):
        try:
            name = request.data['name']
            email = request.data['email']
            phone = request.data['phone']
            is_add_work_wechat_user = request.data.get('is_add_work_wechat_user')
            work_wechat_user_id = request.data.get('work_wechat_user_id')
            coach_type = request.data['coach_type']
        except:
            return parameter_error_response()
        if len(name) > 20:
            return parameter_error_response('姓名最多20个字')
        if len(phone) > 11:
            return parameter_error_response('手机号最多11位')
        elif not phone.isdigit():
            return parameter_error_response('手机号应为数字')

        personal_user = None
        user = User.objects.filter(phone=phone, deleted=False).first()
        if user:
            if Coach.objects.filter(user=user, deleted=False).exists():
                return parameter_error_response('该手机号已注册')
            if ProjectMember.objects.filter(user=user, deleted=False).exists():
                return parameter_error_response('该手机号已注册')
            if PersonalUser.objects.filter(user=user, deleted=False).exists():
                personal_user = PersonalUser.objects.filter(user=user, deleted=False).first()
            else:
                return parameter_error_response('该手机号已注册')
        if not personal_user and User.objects.filter(name=phone, deleted=False).exists():
            return parameter_error_response('该手机号与用户名冲突')
        if ' ' in email:
            return parameter_error_response('邮箱中存在空格，请修改后再添加。')
        if not validate.validate_email(email):
            return parameter_error_response('邮箱格式错误')
        elif len(email) > 50:
            return parameter_error_response('邮箱最多50个字')
        elif User.objects.filter(email=email, deleted=False).exists():
            return parameter_error_response('该邮箱已注册')

        if work_wechat_user_id:
            wx_user = WorkWechatUser.objects.filter(
                user__isnull=False,
                wx_user_id=work_wechat_user_id, deleted=False).first()
            if wx_user:
                return success_response({'is_bind': True, 'user_name': wx_user.user.cover_name})
        if is_add_work_wechat_user:
            wx_user = WorkWechatUser.objects.filter(
                user__isnull=False,
                wx_user_id=phone, deleted=False).first()
            if wx_user:
                return parameter_error_response('手机号对应的企业微信账号已存在')

        with transaction.atomic():
            password = randomPassword()
            pwd = aesencrypt(password)
            if personal_user:
                user = personal_user.user
                user.true_name = name
                user.email = email
                user.password = pwd
                user.save()
            else:
                user_data = {'name': phone, 'email': email, 'true_name': name,
                             'phone': phone, 'password': pwd}
                user = User.objects.create(**user_data)
            coach_data = {'user_id': user.pk, "coach_type": coach_type, "personal_name": name}
            if int(coach_type) == CoachUserTypeEnum.student.value:
                coach_data['is_share_resume'] = False
            coach = Coach.objects.create(**coach_data)
            Resume.objects.create(coach_id=coach.pk, is_customization=False)

            # 获取教练微信小程序二维码数据，生成教练简历链接，发送通知
            if is_add_work_wechat_user:
                state = utils.add_work_wechat_user(user, settings.WORK_WECHAT_COACH_DEPARTMENT_ID)
                if state:
                    return success_response(state)
            else:
                utils.update_work_wechat_user(work_wechat_user_id, user)

        serializer = self.get_serializer(coach)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='编辑教练信息',
        operation_summary='编辑教练信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='姓名'),
                'personal_name': openapi.Schema(type=openapi.TYPE_STRING, description='个人客户可见姓名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description='用户手机号'),
                'work_wechat_user_id': openapi.Schema(type=openapi.TYPE_STRING, description='企业微信用户id'),
                'is_add_work_wechat_user': openapi.Schema(type=openapi.TYPE_STRING, description='是否是新建企业微信账号'),
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练用户id'),
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练id'),
                'english_name': openapi.Schema(type=openapi.TYPE_NUMBER, description='英文名'),
                'birthday': openapi.Schema(type=openapi.TYPE_STRING, description='生日'),
                'gender': openapi.Schema(type=openapi.TYPE_NUMBER, description='性别'),
                'highest_degree': openapi.Schema(type=openapi.TYPE_NUMBER, description='最高学历 1-专科 2-本科 3-硕士 4-博士'),
                'school': openapi.Schema(type=openapi.TYPE_STRING, description='毕业学校'),
                'order_receiving_status': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='接单状态'),
                'coach_type': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='教练类型 1：见习教练，2：个人教练，3：初级教练，4：中级教练，5：高级教练， 6：资深教练'),
                'expected_hourly_salary': openapi.Schema(type=openapi.TYPE_NUMBER, description='1-100元/小时，'
                                                                                                '2-200元/小时， '
                                                                                                '3-300元/小时， '
                                                                                                '4-400元/小时， '
                                                                                                '-500元/小时， '
                                                                                                '6-600元/小时，'
                                                                                                '7-700元/小时， '
                                                                                                '8-800元/小时， '
                                                                                                '9-900元/小时， '
                                                                                                '10-1000元/小时'),
                'languag': openapi.Schema(type=openapi.TYPE_STRING, description='语言'),
                'user_class': openapi.Schema(type=openapi.TYPE_STRING, description='班次'),
                'address': openapi.Schema(type=openapi.TYPE_STRING, description='收件地址'),
                'head_image_url': openapi.Schema(type=openapi.TYPE_STRING, description='证件照'),
                'posters_text': openapi.Schema(type=openapi.TYPE_STRING, description='海报文本信息'),
                'price': openapi.Schema(type=openapi.TYPE_INTEGER, description='辅导价格'),
                'city': openapi.Schema(type=openapi.TYPE_STRING, description='所在城市'),
            }
        ),
        tags=['签约教练相关']
    )
    @action(methods=['post'], detail=False, url_path='update', serializer_class=CoachEditSerializer)
    def edit_coach(self, request, *args, **kwargs):
        try:
            coach_id = request.data.get('id')
            coach = Coach.objects.get(pk=coach_id, deleted=False)
        except:
            return parameter_error_response()
        data = request.data
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        return success_response(serializer.update_coach(validated_data))

    @swagger_auto_schema(
        operation_id='批量导入签约教练',
        operation_summary='批量导入签约教练',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'file': openapi.Schema(type=openapi.TYPE_FILE, description='批量添加签约教练文件（xlsx/xls）'),
            }),
        tags=['签约教练相关']
    )
    @action(methods=['post'], detail=False, url_path='bulk_import_coach')
    def bulk_import_coach(self, request, *args, **kwargs):
        try:
            file = request.FILES.get('file')
            state, coaches_data, error = get_all_coaches(file)
            if not state:
                return parameter_error_response(f'模版错误:{error}', {"is_template": 1})
            elif error:
                return parameter_error_response(err=error)

            with transaction.atomic():
                for coach in coaches_data:
                    password = randomPassword()
                    pwd = aesencrypt(password)
                    if coach.get('user'):
                        user = coach.get('user')
                        user.true_name = coach.get('name')
                        user.email = coach.get('email')
                        user.password = pwd
                        user.save()
                    else:
                        user = User.objects.create(
                            name=coach.get('phone'),
                            password=pwd,
                            true_name=coach.get('name'),
                            email=coach.get('email'),
                            phone=coach.get('phone'))
                    coach_data = {"user_id": user.pk, "coach_type": int(coach['coach_type']),
                                  "user_class": coach['user_class']} if 'user_class' in coach \
                        else {"user_id": user.pk, "coach_type": int(coach['coach_type'])}
                    coach_data['personal_name'] = user.true_name
                    if int(coach['coach_type']) == CoachUserTypeEnum.student.value:
                        coach_data['is_share_resume'] = False
                    coach = Coach.objects.create(**coach_data)
                    resume = Resume.objects.create(coach_id=coach.pk, is_customization=False)

                    # 2.14.4移除创建教练时自动发送教练简历功能
                    # 获取教练微信小程序二维码数据，生成教练简历链接，发送通知
                    # task.send_coach_minaapp_resume_email.delay(resume.id, user)
        except Exception as e:
            return parameter_error_response(str(e))
        return success_response()

    @swagger_auto_schema(
        operation_id='签约教练项目列表',
        operation_summary='签约教练项目列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('name', openapi.IN_QUERY, description='项目名称', type=openapi.TYPE_STRING),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='教练用户id(必传)', type=openapi.TYPE_NUMBER),
            openapi.Parameter('add_interview', openapi.IN_QUERY, description='是否为预约辅导项目列表', type=openapi.TYPE_NUMBER)
        ],
        tags=['签约教练相关']
    )
    @action(methods=['get'], detail=False, url_path='project_list', serializer_class=CoachProjectSerializer)
    def project_list(self, request, *args, **kwargs):
        try:
            name = request.query_params.get('name', None)
            user_id = request.query_params.get('user_id', None)
            add_interview = request.query_params.get('add_interview', 0)
        except:
            return parameter_error_response()
        if not add_interview:
            if not user_id:
                return parameter_error_response()
            project_lst = list(set(ProjectCoach.objects.filter(coach__user_id=user_id,
                                                               member__isnull=True,
                                                               project_group_coach__isnull=True,
                                                               deleted=False).values_list('project_id', flat=True)))
            project = Project.objects.filter(id__in=project_lst, deleted=False).order_by('-created_at')
            if name:
                project = project.filter(name__icontains=name)
        else:
            now = datetime.datetime.now()
            project = Project.objects.filter(start_time__lte=now, end_time__gte=now,
                                             deleted=False).exclude(
                status__in=ProjectStatusEnum.admin_data_viewing()).order_by('-created_at')

        project_lst = [[CoachProjectSerializer().get_remain_time(p), p] for p in project]
        project_lst = sorted(project_lst, key=lambda k: k[0], reverse=True)
        project_lst = [p[1] for p in project_lst]

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(project_lst, self.request)
        serializer = self.get_serializer(page_list, many=True, context={"user_id": user_id})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='签约教练辅导记录列表',
        operation_summary='签约教练辅导记录列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('name', openapi.IN_QUERY, description='被教练姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='教练用户id(必传)', type=openapi.TYPE_NUMBER),
            openapi.Parameter('type', openapi.IN_QUERY, description='辅导记录状态 1-待辅导 2-已完成 3-已取消', type=openapi.TYPE_NUMBER)
        ],
        tags=['签约教练相关']
    )
    @action(methods=['get'], detail=False, url_path='interview_record_list', serializer_class=InterviewRecordSerializer)
    def interview_record_list(self, request, *args, **kwargs):
        try:
            name = request.query_params.get('name', None)
            user_id = request.query_params['user_id']
            type = int(request.query_params.get('type', 0))
            project_ids = request.query_params.get('project_ids')
        except:
            return parameter_error_response()
        project_interview = ProjectInterview.objects.filter(public_attr__user_id=user_id, deleted=False).order_by(
            '-public_attr__start_time').exclude(public_attr__project__isnull=True)
        if project_ids:
            project_interview = project_interview.filter(public_attr__project_id__in=project_ids.split(','))
        now = datetime.datetime.now()
        # 待辅导
        not_start_count = project_interview.filter(public_attr__start_time__gt=now).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).count()
        # 已完成
        is_completed_count = project_interview.filter(public_attr__end_time__lt=now).exclude(
            public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).count()
        # 已取消
        cancel_count = project_interview.filter(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).count()

        if name:
            project_interview = project_interview.filter(Q(public_attr__target_user__name__icontains=name) |
                                                         Q(public_attr__target_user__true_name__icontains=name))
        if type:
            if type == 1:
                project_interview = project_interview.filter(public_attr__start_time__gt=now).exclude(
                    public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
            elif type == 2:
                project_interview = project_interview.filter(public_attr__end_time__lt=now).exclude(
                    public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)
            else:
                project_interview = project_interview.filter(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL)

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(project_interview, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        response.data['not_start_count'] = not_start_count
        response.data['is_completed_count'] = is_completed_count
        response.data['cancel_count'] = cancel_count
        return success_response(response)


    @swagger_auto_schema(
        operation_id='签约教练被教练者列表',
        operation_summary='签约教练被教练者列表',
        manual_parameters=[
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('name', openapi.IN_QUERY, description='被教练姓名', type=openapi.TYPE_STRING),
            openapi.Parameter('user_id', openapi.IN_QUERY, description='教练用户id(必传)', type=openapi.TYPE_NUMBER)
        ],
        tags=['签约教练相关']
    )
    @action(methods=['get'], detail=False, url_path='project_member_list', serializer_class=CoachProjectMemberSerializer)
    def project_member_list(self, request, *args, **kwargs):
        try:
            name = request.query_params.get('name', None)
            user_id = request.query_params['user_id']
            project_ids = request.query_params.get('project_ids')
        except:
            return parameter_error_response()
        project_interview = ProjectInterview.objects.filter(
            type=INTERVIEW_TYPE_COACHING,
            public_attr__user_id=user_id, place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
            public_attr__project__isnull=False, deleted=False)
        if project_ids:
            project_interview = project_interview.filter(public_attr__project_id__in=project_ids.split(','))
        if project_interview.count() > 0:
            queryset = multiple_field_distinct(project_interview, ['public_attr.project_id',
                                                                   'public_attr.target_user_id'])
            tmp = [{'project_id': i.public_attr.project_id, 'user_id': i.public_attr.target_user_id} for i in queryset]
            q = Q()
            for item in tmp:
                q.add(Q(**item), Q.OR)
            project_member = ProjectMember.objects.filter(q, deleted=False).order_by('-created_at')
            if name:
                project_member = project_member.filter(Q(user___name__icontains=name) |
                                                       Q(user__true_name__icontains=name))
        else:
            project_member = ProjectMember.objects.none()
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(project_member, self.request)
        serializer = self.get_serializer(page_list, many=True, context={'user_id': user_id})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='简历填写提醒邮件',
        operation_summary='简历填写提醒邮件',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='用户id'),
            }),
        tags=['签约教练相关']
    )
    @action(methods=['post'], detail=False, url_path='remind_resume_email')
    def remind_resume_email(self, request, *args, **kwargs):
        user_id = request.data.get('user_id')
        coach = Coach.objects.filter(user_id=user_id, deleted=False).first()
        request_user = request.user
        if not coach:
            return parameter_error_response()
        user = coach.user
        resume = Resume.objects.filter(coach_id=coach.pk, is_customization=False).first()
        if not resume:
            return parameter_error_response('该教练无简历')
        if coach.coach_type == CoachUserTypeEnum.student.value:
            message_send_email_base.delay('send_internship_coach_resume_notice', {'coach_name': coach.user.cover_name},
                                                 [user.email], receiver_ids=user.id, sender_id=request_user.id)
        else:
            # 获取教练微信小程序二维码数据，生成教练简历链接，发送通知
            notify_public.send_coach_minaapp_resume_email.delay(resume.id, user)
        return success_response()


    @swagger_auto_schema(
        operation_id='教练活动列表',
        operation_summary='教练活动列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='教练用户id(必传)', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条',
                              type=openapi.TYPE_NUMBER),
        ],
        tags=['签约教练相关']
    )
    @action(methods=['get'], detail=False, url_path='activity_list')
    def coach_activity_list(self, request, *args, **kwargs):
        try:
            user_id = request.query_params.get('user_id')
            coach = Coach.objects.get(user_id=user_id, deleted=False)
        except Coach.DoesNotExist:
            return parameter_error_response('教练不存在')
        except Exception as e:
            return parameter_error_response()

        activity_coach = ActivityCoach.objects.filter(
            coach_id=coach.id, deleted=False).order_by('-created_at')
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(activity_coach, self.request)

        data = []
        for item in page_list:
            data.append({
                'id': item.activity.id,
                'theme': item.activity.theme,
                'type': item.activity.type,
                'status': activity_public.get_status_display(item.activity),
                'coachee_count': activity_interview_public.get_pay_user_count(
                    item.activity, [item.coach.user_id], is_set=True),
                'interview_count': activity_interview_public.get_interview_user_count(
                    item.activity, [item.coach.user_id]),
            })
        response = paginator.get_paginated_response(data)
        return success_response(response)
