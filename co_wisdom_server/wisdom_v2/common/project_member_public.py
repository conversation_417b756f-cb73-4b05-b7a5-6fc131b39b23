import math

from django.db.models import Sum

from wisdom_v2 import utils
from utils import randomPassword, aesencrypt
from utils.user_data_collection_constant import sub_to_parent_tag_name
from wisdom_v2.common import user_additional_info_public
from wisdom_v2.enum.user_enum import GenderTypeEnum
from wisdom_v2.common import coach_public, interview_public
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewStatusEnum, ChemicalInterviewCoachSourceEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, ProjectInterviewTypeEnum
from wisdom_v2.enum.project_member_enum import ProjectMemberRoleEnum
from wisdom_v2.enum.service_content_enum import InterviewRecordTemplateRoleEnum, NewCoachTaskTypeEnum, \
    MultipleAssociationRelationTypeEnum, CoachTypeEnum, OneToOneMatchTypeEnum, ProjectCoachStatusEnum
from wisdom_v2.models import ProjectMember, ProjectInterested, GrowthGoals, ProjectBundle, WorkWechatUser, PublicAttr, \
    TotalTemplate, CoachTask, MultipleAssociationRelation, CompanyMember, User, ProjectInterview, OneToOneCoach, \
    ProjectCoach, Coach, CustomerPortrait, UserAdditionalInfo
from wisdom_v2.models_file import ProjectTagGroup, ProjectTagConfig
from wisdom_v2.views.constant import ATTR_TYPE_COACH_TASK, ATTR_STATUS_INTERVIEW_CANCEL, USER_INFO_TO_SCORE, \
    INT_TO_SYMBOL_STR


#获取服务路径数据


def is_project_member_have_data(project_member):
    """
    检查项目成员是否有相关数据。

    :param project_member: 需要检查的项目成员对象  type: obj
    :return: 如果项目成员有相关数据，返回相应的错误信息；否则，返回 None  type: str or None
    """

    if not project_member or not isinstance(project_member, ProjectMember):
        return '未获取到正确的项目用户数据'

    # 是否有利益相关者
    if ProjectInterested.objects.filter(
            master=project_member.user, project=project_member.project, deleted=False).exists():
        return '该项目用户还有利益相关者，禁止移除。'

    # 是否有成长目标
    if GrowthGoals.objects.filter(
            public_attr__user=project_member.user, public_attr__project=project_member.project, deleted=False).exists():
        return '该项目用户还有成长目标，禁止移除。'

    # 是否有服务配置
    if ProjectBundle.objects.filter(project_member=project_member, deleted=False).exists():
        return '该项目用户还有服务配置，禁止移除。'

    return


def get_project_member_stakeholder_data(project_stakeholder):
    """
    根据给定的项目利益相关者对象获取其相关信息。

    :param project_stakeholder: 利益相关者用户对象
    :return: 一个字典对象，包含利益相关者的相关信息。
    """

    if not project_stakeholder:
        return {}

    # 获取与WorkWechatUser关联的用户对象，并选取第一个，如果不存在则返回None
    wx_user = WorkWechatUser.objects.filter(user_id=project_stakeholder.interested.id,
                                            deleted=False).first()

    # 如果wx_user存在，则取其external_user_id，否则external_user_id设为None
    external_user_id = wx_user.external_user_id if wx_user else None

    # 如果project_stakeholder的联系电话存在，则取其联系电话，否则设为'-'
    interested_phone = project_stakeholder.interested.phone if project_stakeholder.interested.phone else '-'

    # 把获得的相关信息放到一个字典对象里，然后返回这个字典
    data = {
        'id': project_stakeholder.id,
        'interested_name': project_stakeholder.interested.cover_name,
        'master_name': project_stakeholder.master.cover_name,
        'relation': project_stakeholder.relation,
        'email': project_stakeholder.interested.email,
        'external_user_id': external_user_id,
        'phone': interested_phone,
    }
    return data


def get_project_member_coachee_data(project_coachee_user):
    """
    根据给定的项目被教练者获取其相关信息。

    :param project_coachee_user: 项目被教练者用户对象
    :return: 一个字典对象，包含项目被教练者的相关信息。
    """

    if not project_coachee_user:
        return {}

    # 获取与WorkWechatUser关联的用户对象，并选取第一个，如果不存在则返回None
    wx_user = WorkWechatUser.objects.filter(
        external_user_id__isnull=False, user=project_coachee_user.id, deleted=False).first()
    external_user_id = wx_user.external_user_id if wx_user else None
    # 把获得的相关信息放到一个字典对象里，然后返回这个字典
    data = {
        "id": project_coachee_user.pk,
        "true_name": project_coachee_user.cover_name,
        "email": project_coachee_user.email,
        "phone": project_coachee_user.phone,
        "external_user_id": external_user_id
    }
    return data


def get_project_member_coach_data(project_coach_user, project_id):
    """
    根据给定的项目利益相关者对象获取其相关信息。

    :param project_coach_user: 教练用户信息
    :param project_id: 项目id
    :return: 一个字典对象，包含教练的相关信息。
    """

    if not project_coach_user or not project_id:
        return {}

    state, resume = coach_public.get_project_coach_resume(project_coach_user.id, project_id)
    working_years = resume.working_years if state else None
    coach_auth = resume.coach_auth if state else None
    domain = resume.domain if state else None
    order_receiving_status = resume.coach.order_receiving_status if state and resume.coach else False
    data = {
        "id": project_coach_user.pk,
        "true_name": project_coach_user.cover_name,
        "gender": project_coach_user.gender,
        "working_years": working_years,
        "coach_auth": coach_auth,
        "domain": domain,
        "order_receiving_status": order_receiving_status,
    }
    return data


def add_coach_task(coach_id, template_id, hours, project_member, project_bundle):
    public_attr = PublicAttr.objects.create(
        user_id=coach_id,
        target_user_id=project_member.user_id,
        project_id=project_member.project_id,
        type=ATTR_TYPE_COACH_TASK,
    )
    template = TotalTemplate.objects.get(
        pk=template_id
    )
    # 任务类型
    if template.write_role == InterviewRecordTemplateRoleEnum.stakeholder:
        task_type = NewCoachTaskTypeEnum.stakeholder_research
    else:
        task_type = NewCoachTaskTypeEnum.default_task
    data_dict = {
        "public_attr_id": public_attr.uuid,
        "hours": hours,
        "template_id": template.id,
        "project_bundle_id": project_bundle.id,
        "type": task_type}

    # 创建教练任务
    coach_task = CoachTask.objects.create(**data_dict)

    # 绑定利益相关者
    if task_type == NewCoachTaskTypeEnum.stakeholder_research:
        interested = ProjectInterested.objects.filter(
            master_id=project_member.user_id,
            project_id=project_member.project_id,
            deleted=False
        )
        # 批量保存
        raw_data = []
        for item in interested:
            raw_data.append(MultipleAssociationRelation(
                main_id=coach_task.id,
                secondary_id=item.id,
                deleted=False,
                type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task
            ))
        MultipleAssociationRelation.objects.bulk_create(raw_data)

        # 如果有教练数据，且立即生效，则立即发送通知
        if coach_id:
            interview_time = interview_public.get_user_interview_time(coach_task.project_bundle.project_member)
            write_condition = coach_task.hours if coach_task.hours else 0
            if write_condition <= interview_time:
                utils.send_stakeholder_coach_task_sms_notice(interested, coach_task)
                utils.send_stakeholder_coach_task_email_notice(interested, coach_task)
    return coach_task


def add_project_member(data, project):
    """
    添加项目成员
    :param data: 用户数据
    :param project: 项目对象
    :return:
    """
    if not data or not project:
        return

    # 拼接用户数据
    user_dict = {
        'name': data.get('name') or data.get('email'),
        'true_name': data.get('true_name') or "用户{}".format(randomPassword(length=4)),
        'email': data.get('email'),
    }
    password = randomPassword()
    pwd = aesencrypt(password) if 'email' in user_dict.keys() else aesencrypt('123456')
    user_dict['password'] = pwd
    user_instance = User.objects.create(**user_dict)

    # 拼接项目成员数据
    company_data = remove_user_data(data)
    company_data['user_id'] = user_instance.id
    company_data['company_id'] = project.company_id
    CompanyMember.objects.create(**company_data)
    ProjectMember.objects.create(project_id=project.id, user_id=user_instance.id,
                                 role=ProjectMemberRoleEnum.coachee.value)
    return


def remove_user_data(data: dict):
    """
    移除用户数据中的敏感信息
    """
    if 'true_name' in data.keys():
        data.pop('true_name')
    if 'phone' in data.keys():
        data.pop('phone')
    if 'email' in data.keys():
        data.pop('email')
    if 'area_code' in data.keys():
        data.pop('area_code')
    if 'name' in data.keys():
        data.pop('name')
    return data


def get_project_member_coach_growth_goals(project_member):
    """
    获取指定项目的成长目标列表，只包括已设置目标用户ID的成长目标，并按创建时间排序。

    :param project_member: 指定的项目用户实例
    :return: 成长目标的查询集
    """
    growth_goals = GrowthGoals.objects.filter(
        public_attr__user_id=project_member.user_id,
        public_attr__project_id=project_member.project_id,
        deleted=False).order_by('created_at')
    return growth_goals


def get_project_member_interview(project_member):
    """
    获取指定项目的辅导访谈列表，只包括在线一对一正式访谈且未删除的访谈，并排除已取消状态的访谈，按开始时间排序。

    :param project_member: 指定的项目用户实例
    :return: 项目辅导访谈的查询集
    """
    project_interview = ProjectInterview.objects.filter(
        public_attr__project_id=project_member.project_id,
        public_attr__target_user_id=project_member.user_id,
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value,
        type=ProjectInterviewTypeEnum.formal_interview.value,
        deleted=False).exclude(
        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).order_by('public_attr__start_time')
    return project_interview


def get_interview_all_times_and_user_time(project_member, one_to_one_coach_type=CoachTypeEnum.online.value):
    """
    计算项目成员在特定类型的教练服务中已使用的总时间和个人使用时间。

    参数:
    - project_member: 项目成员模型实例，用于确定其所属项目和教练资源。
    - one_to_one_coach_type: 教练类型，默认为在线一对一（CoachTypeEnum.online.value）。

    返回:
    - all_times: 当前项目成员在项目中配置的时间总和（小时）。
    - used_times: 当前项目成员在指定类型的教练服务中已使用的时长（小时）。
    """

    # 计算当前项目成员在项目中配置的总时间
    all_times = OneToOneCoach.objects.filter(
        project_bundle__project_member=project_member,
        deleted=False, type=one_to_one_coach_type).aggregate(used_times=Sum('online_time'))
    all_times = all_times.get('used_times', 0) if all_times.get('used_times', 0) else 0
    all_times = int(all_times * 60)

    # 计算当前项目成员使用此类教练服务的时间
    used_times = ProjectInterview.objects.filter(
        place_category=ProjectInterviewPlaceCategoryEnum.online_one_to_one.value, deleted=False,
        type=ProjectInterviewTypeEnum.formal_interview.value,
        public_attr__project=project_member.project,
        public_attr__target_user=project_member.user).exclude(
        public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(used_times=Sum('times'))
    used_times = used_times.get('used_times', 0) if used_times.get('used_times', 0) else 0

    return all_times, used_times


def get_project_member_coach(project_member):
    """
    根据项目成员的匹配类型获取教练查询集和匹配状态。

    参数:
    - project_member: 项目成员对象

    功能描述：
    根据用户一对一教练匹配类型区分
    如果是指定教练。需要看化学面谈的配置信息。具体判断返回的教练数据
    如果是教练池，直接返回有辅导记录的教练列表

    返回:
    - coach_query: 教练查询集
    - selected_status: 是否已选择教练的状态（True表示已选择，False表示未选择）
    """

    coach_query = None
    selected_status = True

    # 如果是指定教练。需要看化学面谈的配置信息。
    if project_member.coach_match_type == OneToOneMatchTypeEnum.appoint_coach.value:
        chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()

        # 配置了化学面谈
        if chemical_interview_module:
            # 已匹配教练
            if chemical_interview_module.coaches.filter(
                    deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected,
                    coach__isnull=False).exists():
                chemical_interview = chemical_interview_module.coaches.filter(
                    deleted=False, chemical_interview_status=ChemicalInterviewStatusEnum.selected,
                    coach__isnull=False)
                coach_query = Coach.objects.filter(id__in=list(chemical_interview.values_list('coach_id', flat=True)))
            # 未匹配教练
            else:
                if chemical_interview_module.coach_source == ChemicalInterviewCoachSourceEnum.system_random:
                    chemical_interview = chemical_interview_module.coaches.filter(deleted=False, coach__isnull=False)
                else:
                    chemical_interview = ProjectCoach.objects.filter(
                        project=project_member.project, resume__isnull=False, member__isnull=True,
                        project_group_coach__isnull=True, deleted=False)
                selected_status = False
                coach_query = Coach.objects.filter(id__in=list(chemical_interview.values_list('coach_id', flat=True)))
        # 未配置化学面谈
        else:
            project_coaches = ProjectCoach.objects.filter(
                member_id=project_member.user_id, deleted=False,
                project=project_member.project, status=ProjectCoachStatusEnum.adopt.value,
                project_group_coach__isnull=True)
            coach_query = Coach.objects.filter(id__in=list(project_coaches.values_list('coach_id', flat=True)))

    # 如果是教练池，直接返回有辅导记录的
    elif project_member.coach_match_type == OneToOneMatchTypeEnum.all_project_coach.value:
        formal_interview_queryset = interview_public.get_formal_interview(
            None, [project_member.user_id], project_member.project_id, None, None)
        coach_query = Coach.objects.filter(user_id__in=list(formal_interview_queryset.values_list('public_attr__user_id', flat=True)))

    return coach_query, selected_status


def get_project_member_customer_info(project_id, coachee_user_id, coach_user_id=None):
    """
    获取项目成员（客户）的详细信息。

    :param project_id: 项目ID
    :param coachee_user_id: 接受辅导的用户ID
    :param coach_user_id: (可选) 辅导员的用户ID
    :return: 包含客户基本信息、挑战、教练认知与期待、利益相关方反馈、其他信息及敏感信息的字典

    信息分为三部分，客户基础信息，教练对客户的补充信息，项目运营对客户的补充信息

    获取客户基础信息-客户信息来源分别有：基础信息User表，客户标签信息ProjectTagGroup表，客户开屏问答信息UserAdditionalInfo

    获取教练数据-CustomerPortrait表（coach_id=教练用户id），化学面谈数据和问卷中客户信息类型题目数据，在做辅导记录时自动更新到CustomerPortrait

    获取项目运营数据-CustomerPortrait表（project_id=项目id）
    """

    user = User.objects.filter(id=coachee_user_id, deleted=False).first()
    if not user:
        return

    # 获取基础信息
    base_info = {
        'city': None,  # 所在城市
        'gender': GenderTypeEnum.get_display(user.gender) if user.gender else None,  # 客户性别
        'function': None,  # 所负责的职能单元
        'age': user.age,  # 客户年龄
        'position': None,  # 职位
        'language': None,  # 教练中使用的语言
        'management_experience': None,  # 管理经验
    }

    # 面临的问题和挑战
    current_challenges = {
        'coachee_content': None,  # 客户目前面临的问题，数据获取需求标签和开屏信息中填写的“其他信息”
        'coach_content': None,  # 教练目前面临的问题
        'project_operations_content': None  # 项目运营目前面临的问题
    }

    # 教练的认识和期待信息
    coaches_cognition_and_expectations = {
        'coachee_content': None,  # 客户认识和期待
        'coach_content': None,  # 教练认识和期待
        'project_operations_content': None  # 项目运营认识和期待

    }

    # 利益相关方的反馈和期待信息
    stakeholder_feedback_and_expectations = {
        'coachee_content': None,  # 客户反馈和期待
        'coach_content': None,  # 教练反馈和期待
        'project_operations_content': None  # 项目运营反馈和期待
    }
    # 其他信息
    other_info = {
        'coachee_content': None,  # 客户其他反馈
        'coach_content': None,  # 教练其他反馈
        'project_operations_content': None  # 项目运营其他反馈
    }
    # 敏感信息
    sensitive_info = {
        'coachee_content': None,  # 客户敏感信息
        'coach_content': None,  # 教练敏感信息
        'project_operations_content': None  # 项目运营敏感信息
    }

    # 获取客户的基础信息
    project_tag_group = ProjectTagGroup.objects.filter(
        project_id=project_id, project_member__user_id=coachee_user_id, deleted=False).first()

    # 如果有标签数据，读取用户基础信息，
    if project_tag_group:
        # 获取ProjectTagGroup的详细信息，包括匹配标签
        tag_data = ProjectTagConfig.objects.filter(
            project_tag_group_id=project_tag_group.id, deleted=False).values_list(
            'tag_object__tag__name', 'tag_object__tag__parent__name').order_by('tag_object__tag__parent__created_at').distinct()

        # 多个信息需要和在一起返回，需要使用“，”分割展示， 先用列表存储，方便后续数据处理
        base_function = []
        base_position = []
        base_language = []
        # "当前工作环境", "管理挑战", "工作状态挑战"需要父级标签名称，使用字典存储
        raw_current_challenges_coachee_content = {}

        # 从标签中获取用户基础信息
        for tag_name, parent_name in tag_data:
            if parent_name == "所在城市":
                base_info['city'] = tag_name
            elif parent_name == "管理者性别" and not base_info.get('gender'):
                base_info['gender'] = tag_name
            elif parent_name == "所负责的职能单元":
                base_function.append(tag_name)
            elif parent_name == "管理者年龄" and not base_info.get('age'):
                base_info['age'] = tag_name
            elif parent_name == "职位":
                base_position.append(tag_name)
            elif parent_name == "教练中使用的语言":
                base_language.append(tag_name)
            elif parent_name == "管理经验":
                base_info['management_experience'] = tag_name

            elif parent_name in ["管理者最近面临的工作环境方面的挑战", "管理者近期在管理方面的挑战", "管理者近期在工作状态方面的挑战"]:
                if raw_current_challenges_coachee_content.get(parent_name):
                    raw_current_challenges_coachee_content[parent_name].append(tag_name)
                else:
                    raw_current_challenges_coachee_content[parent_name] = [tag_name]
        # 拼接数据，字符串分割
        base_info['function'] = '，'.join(base_function) if base_function else None
        base_info['position'] = '，'.join(base_position) if base_position else None
        base_info['language'] = '，'.join(base_language) if base_language else None

        # 如果用户有开屏信息收集的数据，计算“对教练认识和期待”
        user_info = UserAdditionalInfo.objects.filter(user_id=coachee_user_id, all_info__isnull=False).first()
        if user_info:
            user_all_info = user_info.all_info

            coach_readiness = ''

            # 初始化题目分数
            score_to_11, score_to_12, score_to_13, score_to_14, score_to_18, score_to_19, score_to_20, score_to_21, score_to_24 = [0 for _ in range(9)]
            # user_all_info 数据样式 [{"sub": 11, "count": "同意"}, {"sub": 12, "count": "符合"}]
            for item in user_all_info:
                sub = item.get('sub')
                count = item.get('count')
                # 获取11 12 13 14 18 19 20 21 24分数
                if sub == 11:
                    score_to_11 = USER_INFO_TO_SCORE.get(count, 0)
                elif sub == 12:
                    score_to_12 = USER_INFO_TO_SCORE.get(count, 0)
                elif sub == 13:
                    score_to_13 = USER_INFO_TO_SCORE.get(count, 0)
                elif sub == 14:
                    score_to_14 = USER_INFO_TO_SCORE.get(count, 0)
                elif sub == 18:
                    score_to_18 = USER_INFO_TO_SCORE.get(count, 0)
                elif sub == 19:
                    score_to_19 = USER_INFO_TO_SCORE.get(count, 0)
                elif sub == 20:
                    score_to_20 = USER_INFO_TO_SCORE.get(count, 0)
                elif sub == 21:
                    score_to_21 = USER_INFO_TO_SCORE.get(count, 0)
                elif sub == 24:
                    score_to_24 = USER_INFO_TO_SCORE.get(count, 0)

                # "管理挑战", "工作状态挑战"可以自定义回答内容，自定义回答内容不会存储成标签数据。
                # 读取用户开屏信息的回答进行对比，如果出现选项中不存在的，更新到列表中
                elif sub in [15, 16]:
                    # 获取父标签名，用于raw_current_challenges_coachee_content中的key
                    parent_tag_name = sub_to_parent_tag_name.get(str(sub))
                    if parent_tag_name in ["管理者近期在管理方面的挑战", "管理者近期在工作状态方面的挑战"]:

                        # 获取题目下的所有选项
                        options = user_additional_info_public.get_options_by_sub(sub)
                        if options:
                            # 回答不在选项中，添加到用户信息数据中
                            if count not in options:
                                # 可能因为题目变动，错误的将之前的需求标签认为是“其他”的自定义输入内容。
                                # 判断count是不是在需求标签中已经出现过，不要重复添加
                                if raw_current_challenges_coachee_content.get(parent_tag_name) and count not in raw_current_challenges_coachee_content[parent_tag_name]:
                                    raw_current_challenges_coachee_content[parent_tag_name].append(count)
                                else:
                                    raw_current_challenges_coachee_content[parent_tag_name] = [count]

            # 对教练的认知度
            if score_to_20:
                coach_readiness += '对教练的认知度：' + INT_TO_SYMBOL_STR.get(score_to_20, '') + '\n'
            # 对教练的信心度
            if score_to_21:
                coach_readiness += '对教练的信心度：' + INT_TO_SYMBOL_STR.get(score_to_21, '') + '\n'
            # 对教练的开放度
            if score_to_24:
                coach_readiness += '对教练的开放度：' + INT_TO_SYMBOL_STR.get(score_to_24, '') + '\n'
            # 对现状的满意度
            if score_to_13 or score_to_14:
                coach_readiness += '对现状的满意度：' + INT_TO_SYMBOL_STR.get(
                    math.ceil((score_to_13 + score_to_14) / 2), '') + '\n'
            # 对改变的意愿度
            if score_to_11 or score_to_12 or score_to_18 or score_to_19:
                coach_readiness += '对改变的意愿度：' + INT_TO_SYMBOL_STR.get(
                    math.ceil((score_to_11 + score_to_12 + score_to_18 + score_to_19) / 4), '')
            # 将拼接的数据保存
            if coach_readiness:
                coaches_cognition_and_expectations['coachee_content'] = coach_readiness

        # 拼接用户面临的问题和挑战
        # 数据获取需求标签和开屏信息中填写的“其他信息”
        current_challenges_coachee_content = []
        for k, v in raw_current_challenges_coachee_content.items():
            current_challenges_coachee_content.append(f'{k}：{"，".join(v)}')
        current_challenges['coachee_content'] = '\n'.join(current_challenges_coachee_content) if current_challenges_coachee_content else None

    # 获取教练对该客户单独添加的信息。
    if coach_user_id:
        coach_customer_portrait = CustomerPortrait.objects.filter(
            user_id=coachee_user_id, coach_id=coach_user_id, project_id__isnull=True, deleted=False
        )
    else:
        coach_customer_portrait = CustomerPortrait.objects.filter(
            user_id=coachee_user_id, coach_id__isnull=False, project_id__isnull=True, deleted=False
        )
    if coach_customer_portrait.exists():

        # 多个教练信息需要和在一起返回，使用\n分割， 先用列表存储，方便后续数据处理
        current_challenges_coach_content = []
        coaches_cognition_and_expectations_coach_content = []
        stakeholder_feedback_and_expectations_coach_content = []
        other_info_coach_content = []

        # 循环教练对客户的画像信息
        for item in coach_customer_portrait:
            # 指定单独教练，描述中不返回教练名称，并返回敏感信息数据。
            if coach_user_id:
                coach_name = ''
                # 敏感信息
                sensitive_info['coach_content'] = item.sensitive_info
            else:
                coach_name = f'{item.coach.cover_name}教练: '

            # 获取教练的挑战信息
            if item.challenge:
                current_challenges_coach_content.append(f'{coach_name}{item.challenge}')
            # 获取教练的教练认识和期待信息
            if item.coach_expect:
                coaches_cognition_and_expectations_coach_content.append(f'{coach_name}{item.coach_expect}')
            # 获取教练的利益相关方反馈和期待信息
            if item.stakeholder_feedback_and_expectations:
                stakeholder_feedback_and_expectations_coach_content.append(
                    f'{coach_name}{item.stakeholder_feedback_and_expectations}')
            # 获取教练的其他信息
            if item.coach_extra:
                other_info_coach_content.append(f'{coach_name}{item.coach_extra}')

        # 数据组合成字符串
        current_challenges['coach_content'] = '\n'.join(current_challenges_coach_content) if current_challenges_coach_content else None
        coaches_cognition_and_expectations['coach_content'] = '\n'.join(
            coaches_cognition_and_expectations_coach_content) if coaches_cognition_and_expectations_coach_content else None
        stakeholder_feedback_and_expectations['coach_content'] = '\n'.join(
            stakeholder_feedback_and_expectations_coach_content) if stakeholder_feedback_and_expectations_coach_content else None
        other_info['coach_content'] = '\n'.join(other_info_coach_content) if other_info_coach_content else None

    # 查询项目运营人员为该客户单独填写的信息
    project_operations_customer_portrait = CustomerPortrait.objects.filter(
        user_id=coachee_user_id, coach_id__isnull=True, project_id=project_id, deleted=False).first()
    if project_operations_customer_portrait:
        # 收集项目运营的挑战信息
        current_challenges['project_operations_content'] = project_operations_customer_portrait.challenge
        # 收集项目运营的认知与期待信息
        coaches_cognition_and_expectations['project_operations_content'] = project_operations_customer_portrait.coach_expect
        # 收集项目运营的其他信息
        other_info['project_operations_content'] = project_operations_customer_portrait.coach_extra
        # 收集项目运营的利益相关方反馈与期待信息
        stakeholder_feedback_and_expectations['project_operations_content'] = project_operations_customer_portrait.stakeholder_feedback_and_expectations

    # 如果项目标签组存在，则收集其利益相关方反馈与期待信息
    # 以客户需求标签数据优先
    if project_tag_group:
        if project_tag_group.stakeholder_feedback_and_expectations:
            stakeholder_feedback_and_expectations['project_operations_content'] = project_tag_group.stakeholder_feedback_and_expectations
        if project_tag_group.other_info:
            other_info['project_operations_content'] = project_tag_group.other_info
        # Get stakeholder feedback and other info from project tag group
        if project_tag_group:
            stakeholder_feedback_list = []
            other_info_list = []
            
            # Collect stakeholder feedback fields
            if project_tag_group.stakeholder_feedback_from_company_background:
                stakeholder_feedback_list.append(project_tag_group.stakeholder_feedback_from_company_background)
            if project_tag_group.stakeholder_feedback_from_company_customer_info:
                stakeholder_feedback_list.append(project_tag_group.stakeholder_feedback_from_company_customer_info)
            if project_tag_group.stakeholder_feedback_from_company_coach_requirements:
                stakeholder_feedback_list.append(project_tag_group.stakeholder_feedback_from_company_coach_requirements)
                
            # Collect other info fields
            if project_tag_group.other_info_from_company_background:
                other_info_list.append(project_tag_group.other_info_from_company_background)
            if project_tag_group.other_info_from_company_customer_info:
                other_info_list.append(project_tag_group.other_info_from_company_customer_info)
            if project_tag_group.other_info_from_company_coach_requirements:
                other_info_list.append(project_tag_group.other_info_from_company_coach_requirements)
                
            # Join feedback items with newlines and set to coachee_content
            if stakeholder_feedback_list:
                stakeholder_feedback_and_expectations['coachee_content'] = '\n'.join(stakeholder_feedback_list)
                
            # Join other info items with newlines and set to coachee_content
            if other_info_list:
                other_info['coachee_content'] = '\n'.join(other_info_list)

    # 职位从员工信息获取，优先展示
    user_company_member = CompanyMember.objects.filter(user=user).first()
    if user_company_member and user_company_member.position:
        base_info['position'] = user_company_member.position

    data = {
        'base_info': base_info,
        'challenge': current_challenges,
        'coach_expect': coaches_cognition_and_expectations,
        'stakeholder_feedback_and_expectations': stakeholder_feedback_and_expectations,
        'coach_extra': other_info,
        'sensitive_info': sensitive_info,
    }

    return data
