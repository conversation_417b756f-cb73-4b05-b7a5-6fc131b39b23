from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action

from wisdom_v2.views import constant
from wisdom_v2.common import personal_report_public
from wisdom_v2.views.project_docs_action import ProjectDocsListSerializer, ProjectDocsCreateSerializer, \
    ProjectEvaluationReportSerializer
from wisdom_v2.models import ProjectDocs, Project, CoachTask, EvaluationReport, ProjectEvaluationReport, UserBackend, \
    ProjectCoach, PersonalReport
from wisdom_v2.enum.project_enum import ProjectDocsTypeEnum, ProjectEvaluationReportTypeEnum
from wisdom_v2.enum.service_content_enum import NewCoachTaskTypeEnum, PersonalReportTypeEnum
from rest_framework import viewsets
from django.db.models import Q
from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from wisdom_v2.views.constant import MANAGE_EVALUATION
from wisdom_v2.views.coach_task_action import CoachTaskSerlizer


class ProjectDocsViewSet(viewsets.ModelViewSet):
    queryset = ProjectDocs.objects.filter(file__deleted=False, deleted=False,
                                          project_docs_type=ProjectDocsTypeEnum.report.value).order_by('-id')
    serializer_class = ProjectDocsListSerializer

    @swagger_auto_schema(
        operation_id='报告列表',
        operation_summary='报告列表',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id（必传）', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
            openapi.Parameter('name', openapi.IN_QUERY, description='被教练者姓名', type=openapi.TYPE_STRING)
        ],
        tags=['项目报告相关']
    )
    def list(self, request, *args, **kwargs):
        project_docs = self.get_queryset()
        if not request.query_params.get('project_id', None):
            return parameter_error_response('请选择项目')
        name = request.query_params.get('name', None)
        if name:
            project_docs = project_docs.filter(Q(project_member__user__true_name__icontains=name) |
                                               Q(project_interested__master__true_name__icontains=name) |
                                               Q(project_interested__interested__true_name__icontains=name))
        project_docs = project_docs.filter(project_id=request.query_params.get('project_id'))
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(project_docs, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='项目报告-上传报告',
        operation_summary='项目报告-上传报告',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id(必传)'),
                'file': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT,
                                                                                     properties={
                        'file_name': openapi.Schema(type=openapi.TYPE_STRING, description='文件名'),
                        'file_type': openapi.Schema(type=openapi.TYPE_STRING, description='文件类型'),
                        'file_path': openapi.Schema(type=openapi.TYPE_STRING, description='文件路径')
                                                                                     }),
                                       description='oss地址列表'),
                'role': openapi.Schema(type=openapi.TYPE_NUMBER, description='1-被教练者 2-利益相关者'),
                'project_member_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='被教练者id'),
                'project_interested_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='利益相关关系id')
            }
        ),
        tags=['项目报告相关']
    )
    def create(self, request, *args, **kwargs):
        self.serializer_class = ProjectDocsCreateSerializer
        user = request.user
        data = request.data.copy()
        data['creator_id'] = user.id
        request._full_data = data
        return success_response(super().create(request, *args, **kwargs).data)

    @swagger_auto_schema(
        operation_id='教练任务报告及测评报告列表',
        operation_summary='教练任务报告及测评报告列表',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),

        ],
        tags=['项目报告相关']
    )
    @action(methods=['get'], detail=False, url_path='evaluation_coach_task_report_list', authentication_classes=[])
    def evaluation_coach_task_report_list(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            project = Project.objects.get(pk=project_id)
            name = request.query_params.get('name')
        except:
            return parameter_error_response()
        coach_tasks = CoachTask.objects.filter(
            (Q(coach_submit_time__isnull=False, type=NewCoachTaskTypeEnum.default_task) |
             Q(coachee_submit_time__isnull=False, type=NewCoachTaskTypeEnum.default_task)) |
            Q(report_url__isnull=False, type=NewCoachTaskTypeEnum.stakeholder_research),
            project_bundle__project=project, deleted=False)
        evaluation_reports = EvaluationReport.objects.filter(deleted=False, public_attr__project=project).exclude(evaluation__code=MANAGE_EVALUATION)
        project_evaluation_reports = ProjectEvaluationReport.objects.filter(deleted=False, project_id=project_id)
        personal_reports = PersonalReport.objects.filter(project=project, deleted=False)
        if name:
            coach_tasks = coach_tasks.filter(project_bundle__project_member__user__true_name__icontains=name)
            evaluation_reports = evaluation_reports.filter(public_attr__user__true_name__icontains=name)
            personal_reports = personal_reports.filter(user__true_name__icontains=name)

        # 个人报告不返回未完成的“利益相关者访谈纪要”
        # 个人报告不返回未完成的“利益相关者访谈总结报告”
        personal_reports = personal_reports.exclude(
            type=PersonalReportTypeEnum.notes_report.value, pdf_url__isnull=True).exclude(
            type=PersonalReportTypeEnum.summary_report.value, pdf_url__isnull=True)

        evaluation_reports_data = []
        coach_tasks_data = []
        project_evaluation_report_data = []
        personal_report_data = []
        if coach_tasks.exists():
            for coach_task in coach_tasks:

                if coach_task.type == NewCoachTaskTypeEnum.default_task:
                    created_time = coach_task.coach_submit_time.strftime('%Y.%m.%d') if\
                        coach_task.coach_submit_time else coach_task.coachee_submit_time.strftime('%Y.%m.%d') if\
                        coach_task.coach_submit_time or coach_task.coachee_submit_time else '--'
                else:
                    created_time = coach_task.stakeholder_submit_time.strftime('%Y.%m.%d') if\
                        coach_task.stakeholder_submit_time else '--'
                coach_tasks_data.append({
                    'id': coach_task.pk,
                    'name': coach_task.template.title,
                    'type': 1,
                    'coach_name': CoachTaskSerlizer().get_coach_name(coach_task),
                    'coach_task_type': coach_task.type,
                    'coachee_name': CoachTaskSerlizer().get_coachee_name(coach_task),
                    'created_time': created_time})

        if evaluation_reports.exists():
            for evaluation_report in evaluation_reports:
                evaluation_report_config = evaluation_report.evaluation.evaluation_report_config.filter(deleted=False).first()
                evaluation_reports_data.append(
                    {
                        'id': evaluation_report.pk,
                        'name': evaluation_report.evaluation.name,
                        'type': 2 if evaluation_report.evaluation.code == MANAGE_EVALUATION else 3,
                        'coach_name': '',
                        'coachee_name': evaluation_report.public_attr.user.cover_name,
                        'created_time': evaluation_report.created_at.strftime('%Y.%m.%d'),
                        'pdf_url': evaluation_report.pdf_url,
                        "evaluation_report_type": evaluation_report_config.type if evaluation_report_config else None,
                    })
        if project_evaluation_reports.exists():
            project_evaluation_report_data = [{'id': project_evaluation_report.pk,
                                               'name': 'LBI领导力行为指数测评项目报告' if
                                               project_evaluation_report.type ==
                                               ProjectEvaluationReportTypeEnum.LBI_EVALUATION.value
                                               else '教练型管理者测评项目报告',
                                               'type': 4 if project_evaluation_report.type ==
                                                            ProjectEvaluationReportTypeEnum.LBI_EVALUATION.value else 5,
                                               'coach_name': '',
                                               'coachee_name': '',
                                               'created_time': project_evaluation_report.
                                                   created_at.strftime('%Y.%m.%d'),
                                               'is_push_manager': project_evaluation_report.is_push_manager,
                                               'pdf_url': project_evaluation_report.pdf_url}
                                              for project_evaluation_report in project_evaluation_reports]
        if personal_reports.exists():
            personal_report_data = []
            for personal_report in personal_reports:
                # 获取个人报告的教练用户
                coach_user = personal_report_public.get_user_to_coach_user(personal_report)

                personal_report_data.append({
                    "id": personal_report.pk,
                    "name": personal_report.name,
                    "type": 6,
                    "coach_name": coach_user.cover_name if coach_user else None,
                    "personal_report_type": personal_report.type,
                    "coachee_name": personal_report.user.cover_name,
                    "created_time": personal_report.created_at.strftime('%Y.%m.%d'),
                    "pdf_url": personal_report.pdf_url})

        data = evaluation_reports_data + coach_tasks_data + project_evaluation_report_data + personal_report_data

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(data, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response)


class ProjectEvaluationReportViewSet(viewsets.ModelViewSet):
    queryset = ProjectEvaluationReport.objects.all().order_by('-created_at')
    serializer_class = ProjectEvaluationReportSerializer

    @swagger_auto_schema(
        operation_id='报告列表',
        operation_summary='报告列表',
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description='项目id', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
        ],
        tags=['项目测评报告相关']
    )
    def list(self, request, *args, **kwargs):
        try:
            project_id = request.query_params.get('project_id')
            role = request.headers.get('role', None)
        except:
            return parameter_error_response()
        if project_id:
            project = Project.objects.filter(pk=project_id, deleted=False).first()
            if not project:
                return parameter_error_response()
            queryset = ProjectEvaluationReport.objects.filter(project=project, deleted=False)
            if role in [1, '1']:  # 教练
                # 小程序端临时屏蔽LBI项目报告
                queryset = queryset.exclude(type=ProjectEvaluationReportTypeEnum.LBI_EVALUATION.value)
                # 查看当前教练是否为集体辅导教练，如果不是过滤教练型管理者项目报告
                coach_user_id = request.user.pk
                is_group_coach = ProjectCoach.objects.filter(project_id=project_id, deleted=False,
                                                             project_group_coach__isnull=False,
                                                             coach__user_id=coach_user_id).exists()
                if not is_group_coach:
                    queryset = queryset.exclude(type=ProjectEvaluationReportTypeEnum.MANAGE_EVALUATION.value)
        else:
            # 没有传递项目id就试着读取用户负责的项目
            user_id = request.user.pk
            project_ids = UserBackend.objects.filter(
                deleted=False,
                user_id=user_id,
                role=constant.PROJECT_COMPANY_ADMIN).values_list('project_id', flat=True)
            if not project_ids:
                return parameter_error_response()
            queryset = ProjectEvaluationReport.objects.filter(project_id__in=project_ids, deleted=False)
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='项目测评报告详情',
        operation_summary='项目测评报告详情',
        manual_parameters=[
            openapi.Parameter('project_evaluation_report_id', openapi.IN_QUERY, description='项目测评报告id',
                              type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['项目测评报告相关']
    )
    @action(methods=['get'], detail=False, url_path='project_evaluation_report_detail')
    def project_evaluation_report_detail(self, request, *args, **kwargs):
        try:
            project_evaluation_report_id = request.query_params.get('project_evaluation_report_id')
            project_evaluation_report = ProjectEvaluationReport.objects.get(id=project_evaluation_report_id)
        except ProjectEvaluationReport.DoesNotExist:
            return parameter_error_response()
        serializer = ProjectEvaluationReportSerializer(project_evaluation_report)
        data = serializer.data
        return success_response(data)

    @swagger_auto_schema(
        operation_id='项目测评报告-推送',
        operation_summary='项目测评报告-推送',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'project_evaluation_report_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id(必传)'),
            }
        ),
        tags=['项目测评报告相关']
    )
    @action(methods=['post'], detail=False, url_path='push_project_evaluation_report')
    def push_project_evaluation_report(self, request, *args, **kwargs):
        try:
            project_evaluation_report_id = request.data.get('project_evaluation_report_id')
            project_evaluation_report = ProjectEvaluationReport.objects.get(id=project_evaluation_report_id)
        except ProjectEvaluationReport.DoesNotExist:
            return parameter_error_response()
        project_evaluation_report.is_push_manager = True
        project_evaluation_report.save()
        return success_response(ProjectEvaluationReportSerializer(project_evaluation_report).data)