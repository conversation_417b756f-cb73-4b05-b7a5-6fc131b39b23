import datetime
import json
import logging
import redis
import requests
import os

from django.conf import settings

from utils import work_wechat_content
from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils.message.lark_message import LarkMessageCenter
from utils.third_parties_error_log import error_log, sls_log
from utils import redis_public

error_logging = logging.getLogger('api_action')
token_redis = redis.Redis.from_url(settings.TOKEN_LOGIN_REDIS)
work_wechat_redis = redis.Redis.from_url(settings.WORK_WECHAT_REDIS)
work_wechat_http = requests.Session()


# 企业微信自定义requests的hooks，access_token过期后自动重新调用
def work_wechat_hook(response, *args, **kwargs):
    if response.ok:
        data = response.request.body
        body = json.loads(data) if data else {}
        if response.json().get('errcode') in [42001, 40014] and not body.get('check'):

            # 删除缓存
            redis_keys = work_wechat_redis.keys('WorkWechatToken*')
            if redis_keys:
                work_wechat_redis.delete(*redis_keys)

            # 替换url中的access_token参数值
            url = response.request.url
            api, url = url.split('?')
            params = []
            for item in url.split('&'):
                if 'access_token' in item:
                    item = 'access_token={}'.format(WorkWechat().get_access_token())
                params.append(item)
            url = api + '?' + '&'.join(params)

            # 次数标记，防止循环调用。
            if body:
                body['check'] = 'true'
            else:
                body = {'check': 'true'}

            # 记录access_token过期后重新发起的请求
            error_logging.info({'name': api, 'url': url, 'params': body})

            # 重新请求
            if response.request.method == 'POST':
                return work_wechat_http.post(url, json=body)
            elif response.request.method == 'GET':
                return work_wechat_http.get(url, json=body)


work_wechat_http.hooks["response"] = [work_wechat_hook]


class WorkWechat(object):
    """
    企业微信功能类
    对应文档：https://developer.work.weixin.qq.com/document/path/90556
    """

    def __init__(self):
        self.base_url = 'https://qyapi.weixin.qq.com/cgi-bin'
        self.appid = settings.APP_ID
        self.corpid = settings.WORK_WECHAT_CORPID
        self.corpsecret = settings.WORK_WECHAT_CORPSECRET
        self.agentid = settings.WORK_WECHAT_AGENT_ID

    # 获取调用接口凭证
    def get_access_token(self, agentid=None):
        if agentid == '1000013':
            secret = settings.WORK_WECHAT_ASSISTANT_AGENT_SECRET
            agentid = settings.WORK_WECHAT_ASSISTANT_AGENT_ID
        else:
            secret = self.corpsecret
            agentid = self.agentid

        if work_wechat_redis.get('WorkWechatToken-{}'.format(agentid)):
            return work_wechat_redis.get('WorkWechatToken-{}'.format(agentid)).decode()

        url = self.base_url + '/gettoken'
        params = {
            'corpid': self.corpid,
            'corpsecret': secret
        }
        try:
            raw = requests.get(url=url, params=params)
            if raw.json().get('errcode') == 0:
                access_token = raw.json().get('access_token')
                work_wechat_redis.set('WorkWechatToken-{}'.format(agentid), access_token, ex=60*60)
                return access_token

            func_name = '企业微信获取调用接口凭证'
            content = f"params: {str(params)}; url: {url}"
            LarkMessageCenter().send_other_backend_message(f'{func_name}错误', 'error', content)
            AliyunSlsLogLayout().send_third_api_log(content={'error': f'{content}'}, message=func_name)
            return
        except Exception as e:
            func_name = '企业微信获取调用接口凭证'
            content = f"{str(e)}; url: {url}"
            LarkMessageCenter().send_other_backend_message(f'{func_name}错误', 'error', content)
            AliyunSlsLogLayout().send_third_api_log(content={'error': f'{content}'}, message=func_name)
        return

    # 获取客户详情
    @error_log
    def get_user_unionid(self, external_userid):
        """
        :param external_userid: 外部联系人的userid，不是企业成员的帐号
        """

        url = self.base_url + '/externalcontact/get?access_token={}'.format(
            self.get_access_token())

        params = {
            'external_userid': external_userid
        }
        try:
            raw = work_wechat_http.get(url=url, params=params)
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True, raw.json().get('external_contact').get('unionid')
            return False, {'url': url, 'error': raw.text, 'params': params}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    # 发送应用消息
    @sls_log
    @error_log
    def send_message(self, touser, msgtype, content, project_id=None, sender_id=None, receiver_id=None, template_name=None):
        """
        :param touser: 成员UserID。对应管理端的帐号，企业内必须唯一。
        :param content: 文本数据
        :param miniprogram_notice: 小程序数据
        :param project_id: 项目ID
        :param sender_id: 发送者ID
        :param receiver_id: 接收者ID
        :param template_name: 模板名称
        """
        from wisdom_v2.models_file.platform_notification import PlatformNotification
        from wisdom_v2.enum.service_content_enum import NoticeChannelTypeEnum

        url = self.base_url + '/message/send?access_token={}'.format(self.get_access_token())

        params = {
            "touser": touser,
            "msgtype": msgtype,
            "agentid": self.agentid,
        }
        params.update(content)
        try:
            raw = work_wechat_http.post(url=url, json=params)
            success = raw.json().get('errcode') == 0

            # 记录通知信息
            if template_name:
                # 提取消息内容作为主题
                subject = work_wechat_content.content_type_to_message.get(template_name)

                # 如果touser是列表，为每个用户创建通知记录
                if isinstance(touser, list):
                    for user in touser:
                        PlatformNotification.create_notification(
                            subject=subject,
                            template_name=template_name,
                            channel=NoticeChannelTypeEnum.work_wechat.value,
                            project_id=project_id,
                            sender_id=sender_id,
                            receiver_id=receiver_id,
                            receiver_contact=user,
                            status=1 if success else 2,
                            error_message=None if success else str(raw.text),
                            extra_data=params
                        )
                else:
                    # touser是单个用户时创建一条通知记录
                    PlatformNotification.create_notification(
                        subject=subject,
                        template_name=template_name,
                        channel=NoticeChannelTypeEnum.work_wechat.value,
                        project_id=project_id,
                        sender_id=sender_id,
                        receiver_id=receiver_id,
                        receiver_contact=touser,
                        status=1 if success else 2,
                        error_message=None if success else str(raw.text),
                        extra_data=params
                    )

            if success:
                return True, 'ok'
            return False, {'url': url, 'params': params, 'raw': raw.text}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    # 获取教练“来约我”的信息
    @error_log
    def get_contact_way_info(self, wx_user_id):
        """
        :param wx_user_id: 企业微信成员的userid
        """

        url = self.base_url + '/externalcontact/add_contact_way?access_token={}'.format(
            self.get_access_token())

        params = {
            'type': 1,  # 获取单个成员数据
            'scene': 2,  # 二维码模式
            'user': [wx_user_id]
        }
        try:
            raw = work_wechat_http.post(url=url, json=params)
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True, raw.json()
            return False, {'url': url, 'error': raw.text, 'params': params}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    # 添加部门成员
    @sls_log
    @error_log
    def add_department_user(self, user_id, name, mobile, department, email):
        """
        :param user_id: 成员UserID。对应管理端的帐号，企业内必须唯一。
        :param name: 成员名称
        :param mobile: 手机号码。企业内必须唯一，mobile/email二者不能同时为空
        :param department: 成员所属部门id列表，不超过100个
        :param email: 邮箱。长度6~64个字节，且为有效的email格式。企业内必须唯一
        """

        self.corpsecret = settings.WORK_WECHAT_CONTACTS_SECRET
        url = self.base_url + '/user/create?access_token={}'.format(self.get_access_token())

        params = {
            "userid": user_id,
            "name": name,
            "mobile": mobile,
            "department": department,
            "email": email,
        }
        try:
            raw = work_wechat_http.post(url=url, json=params)
            if raw.json().get('errcode') == 0:
                return True, raw.json().get('errmsg')
            elif raw.json().get('errcode') == 60104:
                return True, {'is_exist_phone': True}
            return False, {'url': url, 'error': raw.json().get('errmsg'), 'params': params}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    # 获取部门成员列表
    @error_log
    def get_department_users(self, department_id):
        """
        :param department_id: 部门ID。
        """

        url = self.base_url + '/user/simplelist'
        params = {
            'access_token': self.get_access_token(),
            "department_id": department_id
        }
        try:
            raw = work_wechat_http.get(url=url, params=params)
            if raw.json().get('errcode') == 0:
                return True, raw.json().get('userlist')
            return False, {'url': url, 'error': raw.text, 'params': params}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    # 获取子部门id列表
    @error_log
    def get_department_ids(self, department_id=None):
        """
        :param department_id: 父部门ID。
        """
        url = self.base_url + '/department/simplelist'
        params = {
            'access_token': self.get_access_token(),
            "department_id": department_id
        }
        try:
            raw = work_wechat_http.get(url=url, params=params)
            if raw.json().get('errcode') == 0:
                department_id = [item.get('id') for item in raw.json().get('department_id')]
                return True, department_id
            return False, {'url': url, 'error': raw.text, 'params': params}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    # 添加日历
    def add_calendar(
            self, organizer, summary,
            readonly=None, set_as_default=None,
            description=None, shares=None):

        url = self.base_url + '/oa/calendar/add?access_token={}'.format(
            self.get_access_token())
        params = {
            "calendar": {
                "organizer": organizer,
                "readonly": readonly,
                "set_as_default": set_as_default,
                "summary": summary,
                "color": settings.WORD_WECHAT_CALENDAR_COLOR,
                "description": description,
                "shares": shares
            },
        }
        raw = requests.post(url=url, json=params)
        if raw.ok:
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True, raw.json().get('cal_id')
            return False, raw.json().get('errmsg')
        return False, raw.status_code

    # 更新日历
    def update_calendar(
            self, cal_id, summary,
            readonly=None, description=None, shares=None):

        url = self.base_url + '/oa/calendar/update?access_token={}'.format(
            self.get_access_token())
        params = {
            "calendar": {
                "cal_id": cal_id,
                "readonly": readonly,
                "summary": summary,
                "color": settings.WORD_WECHAT_CALENDAR_COLOR,
                "description": description,
                "shares": shares
            },
        }
        raw = requests.post(url=url, json=params)
        if raw.ok:
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True,
            return False, raw.json().get('errmsg')
        return False, raw.status_code

    #   查看日历列表
    def get_calendar_list(self, cal_ids):
        url = self.base_url + '/oa/calendar/get?access_token={}'.format(
            self.get_access_token())
        raw = requests.post(url=url, json={'cal_id_list': cal_ids})
        if raw.ok:
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True, raw.json().get('calendar_list')
            return False, raw.json().get('errmsg')
        return False, raw.status_code

    # 删除日历
    def del_calendar(self, cal_id):

        url = self.base_url + '/oa/calendar/del?access_token={}'.format(
            self.get_access_token())
        raw = requests.post(url=url, json={'cal_id': cal_id})
        if raw.ok:
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True,
            return False, raw.json().get('errmsg')
        return False, raw.status_code

    # 添加日程
    def add_schedule(
            self, user_id, start_time, end_time,
            summary=None, description=None, reminders=None,
            location=None, cal_id=None,
            attendees=None):

        url = self.base_url + '/oa/schedule/add?access_token={}'.format(
            self.get_access_token())
        params = {
            "schedule": {
                "organizer": user_id,
                "start_time": start_time,
                "end_time": end_time,
                "attendees": attendees,
                "summary": summary,
                "description": description,
                "reminders": reminders,
                "location": location,
                "cal_id": cal_id
            },
        }
        raw = requests.post(url=url, json=params)
        if raw.ok:
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True, raw.json().get('schedule_id')
            return False, raw.json().get('errmsg')
        return False, raw.status_code

    # 修改日程
    def update_schedule(
            self, user_id, start_time, end_time, schedule_id,
            summary=None, description=None, reminders=None,
            location=None, cal_id=None,
            attendees=None):
        url = self.base_url + '/oa/schedule/update?access_token={}'.format(
            self.get_access_token())
        params = {
            "schedule": {
                "organizer": user_id,
                "schedule_id": schedule_id,
                "start_time": start_time,
                "end_time": end_time,
                "attendees": attendees,
                "summary": summary,
                "description": description,
                "reminders": reminders,
                "location": location,
                "cal_id": cal_id
            },
        }
        raw = requests.post(url=url, json=params)
        if raw.ok:
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True,
            return False, raw.json().get('errmsg')
        return False, raw.status_code

    # 获取日程列表
    def get_schedule_list(self, schedule_id):
        url = self.base_url + '/oa/schedule/get?access_token={}'.format(
            self.get_access_token())
        raw = requests.post(url=url, json={'schedule_id_list': schedule_id})
        if raw.ok:
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True, raw.json().get('schedule_list')
            return False, raw.json().get('errmsg')
        return False, raw.status_code

    # 取消日程
    def del_schedule(self, schedule_id):
        url = self.base_url + '/oa/schedule/update?access_token={}'.format(
            self.get_access_token())
        raw = requests.post(url=url, json={'schedule_id': schedule_id})
        if raw.ok:
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True,
            return False, raw.json().get('errmsg')
        return False, raw.status_code

    # 更新日程参与者
    def update_schedule_attendees(self, schedule_id, attendees, method='add'):
        """
        :param method: 对应请求方法 del：删除 add:新增.
        :param attendees: 日程参与者列表.
        :param schedule_id: 日程id.
        """

        url = self.base_url + '/oa/schedule/{}_attendees?access_token={}'.format(
            method, self.get_access_token())

        raw = requests.post(
            url=url,
            json={
                'schedule_id': schedule_id,
                'attendees': attendees,
            })

        if raw.ok:
            if raw.json().get('errcode') == 0 and raw.json().get('errmsg') == 'ok':
                return True,
            return False, raw.json().get('errmsg')
        return False, raw.status_code

    # 获取临时登陆凭证
    @error_log
    def get_code2session(self, code):
        url = self.base_url + '/miniprogram/jscode2session'
        access_token = self.get_access_token()
        if not access_token:
            return False, '{}接口未获取到access_token'.format(url)
        params = {
            'access_token': access_token,
            'js_code': code,
            'grant_type': 'authorization_code'
        }

        try:
            raw = work_wechat_http.get(url=url, params=params)
            if raw.json().get('errcode') == 0:
                session_key, userid, corpid = raw.json().get('session_key'), raw.json().get('userid'), raw.json().get('corpid')
                return True, {"session_key": session_key, "userid": userid, "corpid": corpid}
            return False, {'url': url, 'error': raw.text, 'params': params}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    # 上传临时素材
    @error_log
    def upload_media_image(self, file_name):
        redis_media_id = work_wechat_redis.get(f'{file_name}_media_id')
        if redis_media_id:
            media_id = redis_media_id.decode()
            return True, media_id
        url = self.base_url + '/media/upload'
        access_token = self.get_access_token()
        if not access_token:
            return False, '{}接口未获取到access_token'.format(url)
        params = {
            "access_token": access_token,
            "type": "image"
        }
        file_path = os.path.join(settings.BASE_DIR, "static/" + file_name)
        try:
            raw = requests.post(url=url, params=params, files=[('media', open(file_path, 'rb'))])
            if raw.json().get('errcode') == 0:
                media_id = raw.json().get('media_id')
                redis_public.save_today_end_redis(work_wechat_redis, f'{file_name}_media_id', media_id)
                return True, media_id
            return False, {'url': url, 'error': raw.text, 'params': params, 'file_path': file_path}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params, 'file_path': file_path}

    @error_log
    def upload_media_file(self, file_data, file_type):
        """
        发送文件信息
        : param file_data: (文件名，文件数据) type: tuple
        : param file_type: 文件类型
        """
        url = self.base_url + '/media/upload'
        access_token = self.get_access_token()
        if not access_token:
            return False, '{}接口未获取到access_token'.format(url)
        params = {
            "access_token": access_token,
            "type": file_type
        }
        try:
            raw = requests.post(url=url, params=params, files=[('media', file_data)])
            if raw.json().get('errcode') == 0:
                media_id = raw.json().get('media_id')
                return True, media_id
            return False, {'url': url, 'error': raw.text, 'params': params}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    # 添加企业群发消息
    @error_log
    def add_msg_template(self, external_userid_list, content, file_name, project_id=None, sender_id=None, receiver_id=None, template_name=None):
        from wisdom_v2.models_file.platform_notification import PlatformNotification
        from wisdom_v2.enum.service_content_enum import NoticeChannelTypeEnum

        url = self.base_url + '/externalcontact/add_msg_template'
        access_token = self.get_access_token()
        if not access_token:
            return False, '{}接口未获取到access_token'.format(url)
        params = {
            "access_token": access_token,
        }
        try:
            raw = work_wechat_http.post(url=url, params=params, json=content)
            success = raw.json().get('errcode') == 0

            # 记录通知信息 - 为每个外部用户创建一条记录
            if template_name and external_userid_list:
                # 提取消息内容作为主题
                subject = work_wechat_content.content_type_to_message.get(template_name)

                # 创建通知记录
                PlatformNotification.create_notification(
                    subject=subject,
                    template_name=template_name,
                    channel=NoticeChannelTypeEnum.work_wechat.value,
                    project_id=project_id,
                    sender_id=sender_id,
                    receiver_id=receiver_id,
                    receiver_contact=external_userid_list,
                    status=1 if success else 2,
                    error_message=None if success else str(raw.text),
                    extra_data={'content': content, 'file_name': file_name}
                )
                    

            if success:
                msgid = raw.json().get('msgid')
                for ex in external_userid_list:
                    count = work_wechat_redis.get(ex + '_' + datetime.datetime.now().strftime("%Y-%m-%d"))
                    if count:
                        count = int(count.decode())
                        count += 1
                        redis_public.save_today_end_redis(
                            work_wechat_redis,
                            ex + '_' + datetime.datetime.now().strftime("%Y-%m-%d"),
                            count)
                    else:
                        redis_public.save_today_end_redis(
                            work_wechat_redis,
                            ex + '_' + datetime.datetime.now().strftime("%Y-%m-%d"),
                            1)

                return True, msgid
            return False, {'url': url, 'error': raw.text, 'params': params, 'file_name': file_name}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params, 'file_name': file_name}


    # 获取客户详情
    @error_log
    def get_external_user_data(self, external_user_id):
        url = self.base_url + f'/externalcontact/get'
        access_token = self.get_access_token()
        if not access_token:
            return False, '{}接口未获取到access_token'.format(url)
        params = {
            "access_token": access_token,
            "external_userid": external_user_id
        }
        try:
            raw = work_wechat_http.get(url=url, params=params)
            if raw.json().get('errcode') in [0, 84061]:
                external_contact = raw.json().get('external_contact')
                return True, external_contact
            return False, {'url': url, 'error': raw.text, 'params': params}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    # 获取企业用户的的客户列表
    @error_log
    def get_customer_list(self, user_id):
        url = self.base_url + '/externalcontact/list'
        access_token = self.get_access_token()
        if not access_token:
            return False, '{}接口未获取到access_token'.format(url)
        params = {
            "access_token": access_token,
            "userid": user_id
        }
        try:
            raw = work_wechat_http.get(url=url, params=params)
            if raw.json().get('errcode') in [0, 84061]:
                external_userid = raw.json().get('external_userid')
                return True, external_userid
            return False, {'url': url, 'error': raw.text, 'params': params}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    # 获取企业用户的外部联系人id
    @error_log
    def get_external_user_list(self, userid):
        url = self.base_url + f'/externalcontact/batch/get_by_user?access_token={self.get_access_token()}'
        params = {
            "userid_list": userid,
            "limit": 100
        }
        try:
            raw = work_wechat_http.post(url=url, json=params)
            # 0 成功
            # 84061 不存在外部联系人
            # 60111 成员id错误
            if raw.json().get('errcode') in [0, 84061, 60111]:
                external_contact_list = raw.json().get('external_contact_list')
                return True, external_contact_list
            return False, {'url': url, 'error': raw.text, 'params': params}
        except Exception as e:
            return False, {'url': url, 'error': str(e), 'params': params}

    @sls_log
    @error_log
    def create_meeting(self, admin_userid, title, start_time, duration):
        """
        创建会议
        文档：https://developer.work.weixin.qq.com/document/path/99104

        :param admin_userid: 组织者的UserID
        :param title: 会议主题
        :param start_time: 会议开始时间，Unix时间戳格式
        :param duration: 会议持续时间，int 单位：s
        :return: 成功返回 (True, 会议ID)，失败返回 (False, 错误信息)
        """
        url = self.base_url + '/meeting/create?access_token={}'.format(self.get_access_token())
        params = {
            "admin_userid": admin_userid,
            "title": title,
            "meeting_start": start_time,
            "meeting_duration": duration,
            "invitees": {
                "userid": [admin_userid]
            },
        }
        try:
            raw = requests.post(url=url, json=params)
            if raw.ok and raw.json().get('errcode') == 0:
                return True, raw.json()
            return False, raw.json().get('errmsg')
        except Exception as e:
            return False, str(e)

    @sls_log
    @error_log
    def update_meeting(self, meeting_id, title, start_time, duration):
        """
        修改会议
        文档：https://developer.work.weixin.qq.com/document/path/99047

        :param meeting_id: meeting_id
        :param title: 会议主题
        :param start_time: 会议开始时间，Unix时间戳格式
        :param duration: 会议持续时间，int 单位：s
        :return: 成功返回 (True, 会议ID)，失败返回 (False, 错误信息)
        """
        url = self.base_url + '/meeting/update?access_token={}'.format(self.get_access_token())
        params = {
            "meetingid": meeting_id,
            "title": title,
            "meeting_start": start_time,
            "meeting_duration": duration,
        }
        try:
            raw = requests.post(url=url, json=params)
            if raw.ok and raw.json().get('errcode') == 0:
                return True, raw.json()
            return False, raw.json().get('errmsg')
        except Exception as e:
            return False, str(e)

    # 取消会议
    @sls_log
    @error_log
    def cancel_meeting(self, meeting_id):
        """
        取消会议
        文档：https://developer.work.weixin.qq.com/document/path/99048

        :param meeting_id: 会议ID
        :return: 成功返回 (True, 'ok')，失败返回 (False, 错误信息)
        """
        url = self.base_url + '/meeting/cancel?access_token={}'.format(self.get_access_token())
        params = {
            "meetingid": meeting_id
        }
        try:
            raw = requests.post(url=url, json=params)
            if raw.ok and raw.json().get('errcode') == 0:
                return True, 'ok'
            return False, raw.json().get('errmsg')
        except Exception as e:
            return False, str(e)

    # 获取会议详情
    @error_log
    def get_meeting_details(self, meeting_id):
        """
        获取会议详情
        文档：https://developer.work.weixin.qq.com/document/path/99049

        :param meeting_id: 会议ID
        :return: 成功返回 (True, 会议详情)，失败返回 (False, 错误信息)
        """
        url = self.base_url + '/meeting/get_info?access_token={}'.format(self.get_access_token())
        params = {
            "meetingid": meeting_id
        }
        try:
            raw = requests.post(url=url, json=params)
            if raw.ok and raw.json().get('errcode') == 0:
                return True, raw.json()
            return False, raw.json().get('errmsg')
        except Exception as e:
            return False, str(e)


    @error_log
    def get_agent_config(self, agentid='1000013'):
        if work_wechat_redis.get('WorkWechatJSApiTicket-{}'.format(agentid)):
            return True, {'ticket': work_wechat_redis.get('WorkWechatJSApiTicket-{}'.format(agentid)).decode()}
        url = self.base_url + f'/ticket/get?type=agent_config&access_token={self.get_access_token(agentid=agentid)}'
        try:
            raw = work_wechat_http.get(url=url)
            # 0 成功
            if raw.json().get('errcode') in [0]:
                ticket = raw.json().get('ticket')
                expires_in = raw.json().get('expires_in')
                work_wechat_redis.set('WorkWechatJSApiTicket-{}'.format(agentid), ticket, ex=expires_in)
                return True, {'ticket': ticket}
            return False, {'url': url, 'error': raw.text}
        except Exception as e:
            return False, {'url': url, 'error': str(e)}


    @error_log
    def get_corp_config(self):
        if work_wechat_redis.get('WorkWechatJSApiTicket-corp'):
            return True, {'ticket': work_wechat_redis.get('WorkWechatJSApiTicket-corp').decode()}
        url = self.base_url + f'/get_jsapi_ticket?access_token={self.get_access_token()}'
        try:
            raw = work_wechat_http.get(url=url)
            # 0 成功
            if raw.json().get('errcode') in [0]:
                ticket = raw.json().get('ticket')
                expires_in = raw.json().get('expires_in')
                work_wechat_redis.set('WorkWechatJSApiTicket-corp', ticket, ex=expires_in)
                return True, {'ticket': ticket}
            return False, {'url': url, 'error': raw.text}
        except Exception as e:
            return False, {'url': url, 'error': str(e)}