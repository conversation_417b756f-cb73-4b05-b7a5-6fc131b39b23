import socket


def get_settings():
    host_name = socket.gethostname()
    result = {
        "VM-24-11-ubuntu": "co_wisdom_server.dev",
        "VM-0-3-ubuntu": "co_wisdom_server.test",
        "iZ2zefkop8ot7w0nw01re6Z": "co_wisdom_server.prod",
        "xiaoxin13": "co_wisdom_server.local"
    }
    conf = result.get(host_name, None)
    if not conf:
        conf = "co_wisdom_server.prod"
    return conf
