# 阶段总结和总结约谈邮件发送功能

## 概述
在 `send_interview_notify` 函数中补全了阶段总结和总结约谈的判断逻辑和发送邮件代码。该功能通过 `ProjectServiceContent` 配置，`datatype=2` 是教练任务，其中 `template` 的名字用来确定是'阶段总结'还是'终期总结'，`hours` 标识辅导几个小时后填写报告。

## 功能详情

### 1. 判断逻辑

#### 数据查询
- 查询项目中配置的教练任务：`ProjectServiceContent.objects.filter(content_type=DataType.coach_tasks.value)`
- 遍历每个教练任务配置的 `content` 字段中的任务列表
- 获取每个任务的 `template_id` 和 `hours` 配置

#### 模板识别
- 通过 `TotalTemplate.objects.filter(id=template_id)` 获取模板信息
- 检查模板的 `title` 字段：
  - 包含 "阶段总结" → 阶段总结任务
  - 包含 "终期总结" 或 "总结"（但不包含"阶段"） → 终期总结任务

#### 触发条件
- 计算用户累计辅导时长（分钟转小时）
- 当 `total_hours >= hours` 时，触发相应的总结约谈邮件

#### 优先级处理
- **终期总结优先级更高**：当同时满足阶段总结和终期总结条件时，优先选择终期总结
- 按优先级排序：终期总结(priority=1) > 阶段总结(priority=2)
- 同优先级按小时数倒序，选择最新达到的总结

### 2. 邮件发送

#### 阶段总结约谈邮件 (`is_step_summary = True`)

**给教练的邮件**
- 模板类型：`interview_add_phase_summary_coach`
- 参数：
  - `interview_time`: 约谈时间
  - `target_user`: 被教练者姓名
  - `project_name`: 项目名称
  - `summary_name`: 总结模板名称
  - `summary_hours`: 触发小时数
  - `total_hours`: 累计辅导时长

**给被教练者的邮件**
- 模板类型：`interview_add_phase_summary_coachee`
- 参数：
  - `interview_time`: 约谈时间
  - `coach_name`: 教练姓名
  - `project_name`: 项目名称
  - `summary_name`: 总结模板名称
  - `summary_hours`: 触发小时数
  - `total_hours`: 累计辅导时长

**给管理员的邮件**
- 模板类型：`interview_add_phase_summary_admin`
- 参数：
  - `interview_time`: 约谈时间
  - `coachee_name`: 被教练者姓名
  - `coach_name`: 教练姓名
  - `project_name`: 项目名称
  - `summary_name`: 总结模板名称
  - `summary_hours`: 触发小时数
  - `total_hours`: 累计辅导时长
- 抄送：项目经理邮箱

#### 终期总结约谈邮件 (`is_final_summary = True`)

**给教练的邮件**
- 模板类型：`interview_add_final_summary_coach`
- 参数：同阶段总结

**给被教练者的邮件**
- 模板类型：`interview_add_final_summary_coachee`
- 参数：同阶段总结

**给管理员的邮件**
- 模板类型：`interview_add_final_summary_admin`
- 参数：同阶段总结
- 抄送：项目经理邮箱

## 代码修改

### 1. 新增 import
```python
from wisdom_v2.models import TotalTemplate
from wisdom_v2.models_file.project_service import ProjectServiceContent
from wisdom_v2.enum.project_interview_enum import DataType
```

### 2. 判断逻辑实现
```python
# 查找项目中配置的教练任务（datatype=2），检查是否有阶段总结或终期总结
project_service_contents = ProjectServiceContent.objects.filter(
    project=project,
    content_type=DataType.coach_tasks.value,
    deleted=False,
    content__isnull=False
)

# 收集所有符合条件的总结任务，按优先级排序（终期总结优先于阶段总结）
matching_summaries = []

for service_content in project_service_contents:
    if service_content.content:
        for coach_task_config in service_content.content:
            template_id = coach_task_config.get('template_id')
            hours = coach_task_config.get('hours')
            
            if template_id and hours:
                template = TotalTemplate.objects.filter(
                    id=template_id, 
                    deleted=False
                ).first()
                
                if template and template.title:
                    if total_hours >= hours:
                        # 检查是否为终期总结（优先级更高）
                        if '终期总结' in template.title or ('总结' in template.title and '阶段' not in template.title):
                            matching_summaries.append({
                                'type': 'final',
                                'template_name': template.title,
                                'hours': hours,
                                'priority': 1
                            })
                        # 检查是否为阶段总结
                        elif '阶段总结' in template.title:
                            matching_summaries.append({
                                'type': 'step',
                                'template_name': template.title,
                                'hours': hours,
                                'priority': 2
                            })

# 按优先级排序，选择优先级最高的总结类型
if matching_summaries:
    matching_summaries.sort(key=lambda x: (x['priority'], -x['hours']))
    selected_summary = matching_summaries[0]
    
    if selected_summary['type'] == 'final':
        is_final_summary = True
        summary_template_name = selected_summary['template_name']
        summary_hours = selected_summary['hours']
    elif selected_summary['type'] == 'step':
        is_step_summary = True
        summary_template_name = selected_summary['template_name']
        summary_hours = selected_summary['hours']
```

### 3. 邮件发送实现
```python
elif is_step_summary:
    # 发送阶段总结约谈提醒邮件
    push_v2_message.delay(coach_user, 'interview_add_phase_summary_coach', param={...}, project_id=project.id)
    push_v2_message.delay(target_user, 'interview_add_phase_summary_coachee', param={...}, project_id=project.id)
    push_v2_message.delay(admin_user, 'interview_add_phase_summary_admin', param={...}, project_id=project.id, copy_email=[...])

elif is_final_summary:
    # 发送终期总结约谈提醒邮件
    push_v2_message.delay(coach_user, 'interview_add_final_summary_coach', param={...}, project_id=project.id)
    push_v2_message.delay(target_user, 'interview_add_final_summary_coachee', param={...}, project_id=project.id)
    push_v2_message.delay(admin_user, 'interview_add_final_summary_admin', param={...}, project_id=project.id, copy_email=[...])
```

## 测试验证

### 测试用例
- ✅ 累计时长 5.0h → 无匹配
- ✅ 累计时长 10.0h → 阶段总结（触发时长：10h）
- ✅ 累计时长 15.0h → 阶段总结（触发时长：10h）
- ✅ 累计时长 20.0h → 终期总结（触发时长：20h，优先级更高）
- ✅ 累计时长 25.0h → 终期总结（触发时长：20h，优先级更高）

### 验证结果
- ✅ 判断逻辑正确
- ✅ 优先级处理正确（终期总结优先于阶段总结）
- ✅ 邮件模板结构完整
- ✅ 参数传递正确
- ✅ 代码语法检查通过

## 需要创建的邮件模板

需要在数据库的 `app_messagetemplates` 表中创建以下6个邮件模板：

1. `interview_add_phase_summary_coach` - 阶段总结约谈提醒-教练
2. `interview_add_phase_summary_coachee` - 阶段总结约谈提醒-被教练者
3. `interview_add_phase_summary_admin` - 阶段总结约谈提醒-管理员
4. `interview_add_final_summary_coach` - 终期总结约谈提醒-教练
5. `interview_add_final_summary_coachee` - 终期总结约谈提醒-被教练者
6. `interview_add_final_summary_admin` - 终期总结约谈提醒-管理员

## 使用场景

1. **项目配置**：在项目服务内容中配置教练任务，设置模板和触发小时数
2. **自动触发**：当用户累计辅导时长达到配置要求时，系统自动发送相应的总结约谈邮件
3. **优先级处理**：确保终期总结优先于阶段总结，避免重复发送
4. **多角色通知**：同时通知教练、被教练者和管理员，确保信息同步
