"""
平台通知管理视图
"""
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import viewsets, serializers
from rest_framework.decorators import action
from django.db.models import Q

from wisdom_v2.models import Project, UserBackend
from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.enum.service_content_enum import NoticeChannelTypeEnum
from wisdom_v2.models_file import PlatformNotification
from wisdom_v2.models import Project, UserBackend


class PlatformNotificationSerializer(serializers.ModelSerializer):
    """平台通知序列化器"""
    id = serializers.CharField(help_text='通知ID')
    notice_type = serializers.SerializerMethodField(help_text='通知类型')
    notice_subject = serializers.CharField(source='subject', help_text='通知主题')
    project_name = serializers.SerializerMethodField(help_text='项目名称')
    receiver_name = serializers.SerializerMethodField(help_text='接收者')
    sender_name = serializers.SerializerMethodField(help_text='发送者')
    send_status = serializers.SerializerMethodField(help_text='发送状态')
    send_time = serializers.SerializerMethodField(help_text='发送时间')
    template_name = serializers.CharField(help_text='通知模板名称')
    receiver_contact = serializers.CharField(help_text='接收者联系方式')
    content = serializers.CharField(help_text='通知内容')
    extra_data = serializers.JSONField(help_text='额外数据')

    def get_notice_type(self, obj):
        """获取通知类型"""
        try:
            return NoticeChannelTypeEnum(obj.channel).describe()
        except:
            return "未知类型"

    def get_send_status(self, obj):
        """获取发送状态"""
        return "成功" if obj.status == 1 else "失败"

    def get_send_time(self, obj):
        """获取发送时间"""
        return obj.created_at.strftime('%Y-%m-%d %H:%M:%S')

    def get_project_name(self, obj):
        """获取项目名称"""
        if obj.project:
            return obj.project.full_name
        return "--"

    def get_sender_name(self, obj):
        """获取发送者名称"""
        if obj.sender:
            return obj.sender.cover_name
        return "--"

    def get_receiver_name(self, obj):
        """获取接收者名称"""
        if obj.receiver:
            return obj.receiver.cover_name
        return "--"

    class Meta:
        model = PlatformNotification
        fields = ['id', 'notice_type', 'notice_subject', 'project_name',
                  'receiver_name', 'sender_name', 'send_status', 'send_time',
                   'content', 'template_name', 'receiver_contact', 'extra_data']


class PlatformNotificationViewSet(viewsets.ViewSet):
    """平台通知管理视图集"""

    @swagger_auto_schema(
        operation_id='管理后台通知管理列表',
        operation_summary='管理后台通知管理列表',
        manual_parameters=[
            openapi.Parameter('channel', openapi.IN_QUERY, description='通知类型', type=openapi.TYPE_INTEGER),
            openapi.Parameter('subject', openapi.IN_QUERY, description='通知主题', type=openapi.TYPE_STRING),
            openapi.Parameter('project_name', openapi.IN_QUERY, description='项目名称', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='页码', type=openapi.TYPE_INTEGER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页数量', type=openapi.TYPE_INTEGER),
        ],
        tags=['管理后台通知管理']
    )
    @action(methods=['get'], detail=False, url_path='list')
    def notification_list(self, request, *args, **kwargs):
        """
        获取通知列表
        根据用户角色进行权限控制：
        - 管理员可以查看所有项目和个人辅导通知
        - 项目运营和客户顾问只能查看其负责的项目通知
        """
        try:
            # 获取查询参数
            channel = request.query_params.get('channel')
            subject = request.query_params.get('subject')
            project_name = request.query_params.get('project_name')
            receiver_name = request.query_params.get('receiver_name')
            sender_name = request.query_params.get('sender_name')
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')

            # 查询通知记录
            queryset = PlatformNotification.objects.filter(deleted=False).order_by('-created_at')
            # 根据用户角色进行权限控制
            user = request.user
            user_backends = UserBackend.objects.filter(user_id=user.pk, role__name__in=['项目运营', '客户顾问'], deleted=False)
            if user_backends.exists():
                project = Project.objects.filter(project_user_backend__user_id=user.id)
                responsible_projects = project.values_list('id', flat=True)
                queryset = queryset.filter(project__id__in=responsible_projects)

            # 应用过滤条件
            if channel:
                queryset = queryset.filter(channel=channel)
            if subject:
                queryset = queryset.filter(subject__icontains=subject)
            if project_name:
                # 使用项目名称或者企业名称查询
                queryset = queryset.filter(Q(project__name__icontains=project_name) |
                                           Q(project__company__name__icontains=project_name) |
                                           Q(project__company__short__icontains=project_name))
            if receiver_name:
                queryset = queryset.filter(Q(receiver__true_name__icontains=receiver_name) |
                                           Q(receiver__phone__icontains=receiver_name) |
                                           Q(receiver__email__icontains=receiver_name))
            if sender_name:
                queryset = queryset.filter(Q(sender__true_name__icontains=sender_name) |
                                           Q(sender__phone__icontains=sender_name) |
                                           Q(sender__email__icontains=sender_name))
            if start_date:
                queryset = queryset.filter(created_at__date__gte=start_date)
            if end_date:
                queryset = queryset.filter(created_at__date__lte=end_date)


            # 分页
            paginator = StandardResultsSetPagination()
            page_list = paginator.paginate_queryset(queryset, request)
            serializer = PlatformNotificationSerializer(page_list, many=True)
            response = paginator.get_paginated_response(serializer.data)
            return success_response(response)

        except Exception as e:
            return parameter_error_response(str(e))
