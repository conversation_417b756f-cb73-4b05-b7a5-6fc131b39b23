import json

import requests
import logging
import time
import datetime

from django.conf import settings
from utils.env_settings import get_settings

from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum
from wisdom_v2.models_file.platform_notification import PlatformNotification
from wisdom_v2.enum.service_content_enum import NoticeChannelTypeEnum
from celery import shared_task



error_logging = logging.getLogger('api_action')


class LarkMessageCenter:
    """
    飞书消息发送类
    """
    def __init__(self):
        self.headers = {"Content-Type": "application/json"}

    def send(self, url, data):
        requests.request("POST", url, headers=self.headers, data=json.dumps(data))

    def get_robot_url(self, message_type):
        """
        message_type: 消息类型
        1： 500错误信息发送
        2： 400错误信息发送
        """
        is_product = True if settings.SITE_URL == 'https://www.qzcoach.com/' else False
        # 后端自用错误监控
        if message_type in [LarkMessageTypeEnum.server_error, LarkMessageTypeEnum.business_error]:
            if settings.SITE_URL not in ['https://www.qzcoach.com/', 'https://www.qzcoach.cn/',
                                         'https://www.qzcoachtest.com/']:
                return  # 本地环境不发送通知
            if is_product:
                if message_type == LarkMessageTypeEnum.server_error:
                    return settings.LARK_ERROR_MESSAGE_ROBOT
                else:
                    return settings.LARK_BUSINESS_ERROR_MESSAGE_ROBOT
            else:
                if message_type == LarkMessageTypeEnum.server_error:
                    return settings.LARK_ERROR_MESSAGE_ROBOT_DEV_TEST
                else:
                    return settings.LARK_BUSINESS_ERROR_MESSAGE_ROBOT_DEV_TEST
        # celery 健康监测
        elif message_type == LarkMessageTypeEnum.celery_health_check:
            return settings.LARK_CELERY_ROBOT
        # 后端其他服务提醒
        elif message_type == LarkMessageTypeEnum.others_backend_error:
            if is_product:
                return settings.LARK_ERROR_MESSAGE_ROBOT
            else:
                return settings.LARK_ERROR_MESSAGE_ROBOT_DEV_TEST
        # 化学面谈类通知
        elif message_type in LarkMessageTypeEnum.chemical_interview_choice():
            if is_product:
                return settings.LARK_CHEMICAL_INTERVIEW_ROBOT
            else:
                return settings.DEV_TEST_LARK_ROBOT

        # 利益相关者访谈类通知
        elif message_type in LarkMessageTypeEnum.stakeholder_interview_choice():
            if is_product:
                return settings.LARK_STAKEHOLDER_INTERVIEW_ROBOT
            else:
                return settings.DEV_TEST_LARK_ROBOT

        # 利益相关者访谈类通知
        elif message_type == LarkMessageTypeEnum.change_observation_personal_report.value:
            if is_product:
                return settings.LARK_STAKEHOLDER_INTERVIEW_ROBOT
            else:
                return settings.DEV_TEST_LARK_ROBOT
        # 教练更新简历
        elif message_type in LarkMessageTypeEnum.coach_choice():
            if is_product:
                return settings.PERSONAL_APPLY_PROJECT_LARK_ROBOT
            else:
                return settings.DEV_TEST_LARK_ROBOT

        # 项目结束时间临期
        elif message_type == LarkMessageTypeEnum.project_end_date_check.value:
            if is_product:
                return settings.LARK_PROJECT_ROBOT
            else:
                return settings.DEV_TEST_LARK_ROBOT

        elif message_type == LarkMessageTypeEnum.change_observation_personnel_selection.value:
            if is_product:
                return settings.LARK_CHANGE_OBSERVATION_ROBOT
            else:
                return settings.DEV_TEST_LARK_ROBOT

        elif message_type == LarkMessageTypeEnum.user_info_collect.value:
            if is_product:
                return settings.LARK_USER_INFO_COLLECT_ROBOT
            else:
                return settings.DEV_TEST_LARK_ROBOT

        elif message_type == LarkMessageTypeEnum.project_interview_add.value:
            if is_product:
                return settings.LARK_PROJECT_ROBOT
            else:
                return settings.DEV_TEST_LARK_ROBOT

        elif message_type == LarkMessageTypeEnum.app_feedback.value:
            if is_product:
                return settings.LARK_ERROR_MESSAGE_ROBOT
            else:
                return settings.DEV_TEST_LARK_ROBOT


    def get_env(self):
        env = get_settings().split('.')[1]
        tmp_dict = {'prod': '正式环境', 'test': '测试环境', 'dev': '开发环境', 'settings': '本地', 'local': '本地'}
        env_str = tmp_dict[env]
        return env_str

    def send_server_error(self, method, url, token, user_id, params, body, error, role, true_name):
        """
        服务器错误发送消息提醒
        """
        error = "".join(error)
        time_str = str(time.time()).split('.')[0][-4:]
        env_str = self.get_env()
        num = datetime.datetime.now().strftime("%Y%m%d") + env_str + time_str
        text = """
        API 接口错误
        编号: {}
        环境: {}
        级别: error
        method: {}
        role: {}
        url: {}
        token: {}
        user_id: {}
        true_name: {}
        params: {}
        body: {}
        error: {}
            """.format(num, env_str, method, role, url, token, user_id, true_name, params, body, error)
        data = {"msg_type": "text", "content": {"text": text}}
        robot_url = self.get_robot_url(message_type=LarkMessageTypeEnum.server_error)
        if not robot_url:
            return
        self.send(robot_url, data)

    def send_business_error(self, method, url, token, user_id, params, body, msg, role, true_name):
        """
        业务处理拦截发送消息提醒
        """
        time_str = str(time.time()).split('.')[0][-4:]
        env_str = self.get_env()
        num = datetime.datetime.now().strftime("%Y%m%d") + env_str + time_str
        text = """
        API 接口400业务拦截
        编号: {}
        环境: {}
        级别: warning
        method: {}
        role: {}
        url: {}
        token: {}
        user_id: {}
        true_name: {}
        params: {}
        body: {}
        msg: {}
                """.format(num, env_str, method, role, url, token, user_id, true_name, params, body, msg)
        data = {"msg_type": "text", "content": {"text": text}}
        robot_url = self.get_robot_url(message_type=LarkMessageTypeEnum.business_error)
        if not robot_url:
            return
        self.send(robot_url, data)

    def send_celery_hanging_message(self, func_name):
        """
        celery 健康监测发送消息提醒
        """
        env_str = self.get_env()
        data = {
            "msg_type": "text",
            "content": {
                "text": "[{}]celery执行{}任务超时，请关注worker状态是否健康！".format(env_str, func_name)
            }
        }
        robot_url = self.get_robot_url(message_type=LarkMessageTypeEnum.celery_health_check)
        if not robot_url:
            return
        self.send(robot_url, data)

    def send_other_backend_message(self, name, level, content):
        """
        其他后端服务错误提醒
        """
        env_str = self.get_env()
        time_str = str(time.time()).split('.')[0][-4:]
        num = datetime.datetime.now().strftime("%Y%m%d") + env_str + time_str
        text = """
        {}
        编号：{}
        环境：{}
        级别：{}
        内容：{}
            """.format(name, num, env_str, level, content)
        data = {"msg_type": "text", "content": {"text": text}}
        robot_url = self.get_robot_url(message_type=LarkMessageTypeEnum.others_backend_error)
        if not robot_url:
            return
        self.send(robot_url, data)

    def check_robot_url(self, text):
        """
        业务类通知检测到测试账号切换测试机器人
        """
        keywords = ['彭佳丽', '测试', 'testuser__']
        if any(keyword in text for keyword in keywords):
            lark_robot = settings.DEV_TEST_LARK_ROBOT
            return lark_robot
        return

    def get_business_data(self, data, message_type):
        text = None
        if message_type == LarkMessageTypeEnum.chemical_interview_start:
            # 会谈开始3分钟后，任何一方未加入，提醒运营摇人
            text = f"""摇人提醒 {data.get('at_info')}
项目名称: {data.get('project_name')}
客户姓名: {data.get('coachee_name')}
教练姓名: {data.get('coach_name')}
辅导时间: {data.get('interview_time')}
预警信息: {data.get('msg')}
            """
        elif message_type == LarkMessageTypeEnum.chemical_interview_select_coach:
            # 选定教练通知 客户反馈结果设置选择某个教练时，提交成功后发送该消息
            text = f"""选定教练通知
项目名称: {data.get('project_name')}
客户姓名: {data.get('coachee_name')}
教练姓名: {data.get('coach_name')}
匹配结果: 匹配成功
            """
        elif message_type == LarkMessageTypeEnum.chemical_interview_fail:
            # 客户再最后一次面谈结果反馈中选择“不选择”后。通知运营匹配失败
            text = f"""匹配教练失败提醒
项目名称: {data.get('project_name')}
客户姓名: {data.get('coachee_name')}
教练姓名: {data.get('coach_name')}
匹配结果: 匹配失败，面谈机会已用尽
            """
        elif message_type == LarkMessageTypeEnum.chemical_interview_unselect_coach:
            # 客户反馈结果设置不选择某个教练时，提交成功后发送该消息
            text = f"""未选定教练通知
项目名称: {data.get('project_name')}
客户姓名: {data.get('coachee_name')}
教练姓名: {data.get('coach_name')}
匹配结果: 匹配失败
            """
        elif message_type == LarkMessageTypeEnum.chemical_interview_customer_appointment:
            # 2小时一次的化学面谈客户预约情况
            text = f"""化学面谈客户预约情况
项目名称: {data.get('project_name')}
客户预约情况:
{data.get('msg')}
            """
        elif message_type == LarkMessageTypeEnum.chemical_interview_coach_schedule:
            # 化学面谈 日程设置达到截止时间后（发送提醒后+24小时）
            text = f"""化学面谈教练可预约时间
项目名称: {data.get('project_name')}
教练姓名: {data.get('content')}
            """
        elif message_type == LarkMessageTypeEnum.stakeholder_interview_coach_schedule:
            # 利益相关者访谈 日程设置达到截止时间后（发送提醒后+24小时）
            text = f"""利益相关者访谈教练可预约时间
项目名称: {data.get('project_name')}
教练日程: {data.get('content')}
"""
        elif message_type == LarkMessageTypeEnum.stakeholder_interview_start:
            # 利益相关者预约访谈后立刻提醒
            text = f"""利益相关者访谈预约提醒
项目名称: {data.get('project_name')}
客户姓名: {data.get('coachee_name')}
利益相关者: {data.get('stakeholder_name')}
访谈时间: {data.get('interview_time')}
            """
        elif message_type == LarkMessageTypeEnum.coach_task_report:
            # 教练保存利益相关者报告后立刻提醒
            text = f"""{data.get('coach_task_name')}报告已生成
项目名称: {data.get('project_name')}
客户姓名: {data.get('coachee_name')}
教练姓名: {data.get('coach_name')}
            """
        elif message_type == LarkMessageTypeEnum.chemical_interview_reservation_count:
            text = f"""化学面谈教练被预约情况
项目名称：{data.get('project_name')}
教练被预约情况：
{data.get('content')}
            """

        elif message_type == LarkMessageTypeEnum.change_observation_personal_report.value:
            text = f"""改变观察反馈总结报告填写人数不达标提醒
项目名称: {data.get('project_name')}
客户姓名: {data.get('coachee_name')}
需要填写人数：{data.get('total_count')}
已经填写人数: {data.get('completed_count')}
                    """

        elif message_type == LarkMessageTypeEnum.coach_update_resume.value:
            text = f"""教练提交简历申请通知

见习教练提交人数：{len(data.get('student_name', []))}
学员姓名：{'、'.join(data.get('student_name', []))}

个人教练提交人数：{len(data.get('personal_name', []))}
学员姓名：{'、'.join(data.get('personal_name', []))}

企业教练提交人数：{len(data.get('enterprise_name', []))}
学员姓名：{'、'.join(data.get('enterprise_name', []))}
"""

        elif message_type == LarkMessageTypeEnum.project_end_date_check.value:
            text = f"""项目截止风险提醒 \n"""
            for item in data:
                text += f"\n项目名称：{item.get('name')}\n"
                text += f"项目周期：{item.get('date')}\n"

        elif message_type == LarkMessageTypeEnum.stakeholder_interview_personnel_selection.value:
            text = f"""利益相关访谈人员选定提醒 \n"""
            text += f"项目名称：{data.get('project_name')}\n"
            text += f"客户姓名：{data.get('user_name')}\n"

        elif message_type == LarkMessageTypeEnum.change_observation_personnel_selection.value:
            text = f"""改变观察人员选定提醒 \n"""
            text += f"项目名称：{data.get('project_name')}\n"
            text += f"客户姓名：{data.get('user_name')}\n"

        elif message_type == LarkMessageTypeEnum.user_info_collect.value:
            text = f"""个人信息收集提交\n"""
            text += f"项目名称：{data.get('project_name')}\n"
            text += f"客户姓名：{data.get('user_name')}\n"
            text += f"提交内容：{data.get('content')}\n"
            text += f"项目管理地址：{data.get('url')}\n"

        elif message_type == LarkMessageTypeEnum.coach_settlement_create.value:
            text = f"""教练提现/结算单创建提醒\n"""
            text += f"教练：{data.get('coach_name')}\n"
            text += f"创建人：{data.get('create_name')}\n"
            text += f"订单类型：{data.get('type')}\n"
            text += f"订单编号：{data.get('order_ids')}\n"

        elif message_type == LarkMessageTypeEnum.project_interview_add.value:
            text = f"""项目辅导预约通知 {data.get('at_info')}\n"""
            text += f"项目名称：{data.get('project_name')}\n"
            text += f"客户姓名：{data.get('user_name')}\n"
            text += f"教练姓名：{data.get('coach_name')}\n"
            text += f"预约时间：{data.get('interview_time')}\n"

        elif message_type == LarkMessageTypeEnum.app_feedback.value:
            text = f"""App反馈通知\n"""
            text += f"反馈人：{data.get('user_name')}\n"
            text += f"反馈内容：{data.get('content')}\n"

        return text


    @shared_task(queue='default', ignore_result=True)
    def send_business_message(content, message_type, project_id=None, sender_id=None):
        """
        业务类通知
        """
        lark_sender = LarkMessageCenter()
        robot_url = lark_sender.get_robot_url(message_type=message_type)
        if not robot_url:
            return
        text = lark_sender.get_business_data(content, message_type)
        if not text:
            return
        test_robot = lark_sender.check_robot_url(text)
        robot = test_robot if test_robot else robot_url
        data = {"msg_type": "text", "content": {"text": text}}

        # 发送飞书消息
        success = True
        try:
            lark_sender.send(robot, data)
        except Exception as e:
            success = False

        # 记录通知信息
        # 排除测试机器人
        if not test_robot:
            # subject 为text第一行文字
            PlatformNotification.create_notification(
                        subject=text.split('\n')[0],
                        sender_id=sender_id,
                        template_name=message_type,
                        channel=NoticeChannelTypeEnum.feishu.value,
                        project_id=project_id,
                        receiver_contact=robot,
                        status=1 if success else 2
                    )




