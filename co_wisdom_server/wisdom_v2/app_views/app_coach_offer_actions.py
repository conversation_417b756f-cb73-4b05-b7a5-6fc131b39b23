import datetime

from utils.feishu_robot import send_lark_message
from rest_framework import serializers
from django.db import transaction

from utils.api_response import WisdomValidationError
from wisdom_v2.enum.service_content_enum import CoachOfferStatusEnum, CustomerPortraitTypeEnum, ProjectCoachStatusEnum, \
    CoachSourceTypeEnum
from wisdom_v2.common import project_coach_public
from wisdom_v2.models import CoachOffer


class AppCoachOfferSerializer(serializers.ModelSerializer):
    id = serializers.CharField(help_text='id')
    remain_time = serializers.SerializerMethodField(help_text='剩余时间')
    project_info = serializers.SerializerMethodField(help_text='项目信息')
    price = serializers.CharField(source='project_offer.price', help_text='教练费用描述')
    status = serializers.IntegerField(help_text='状态')
    max_customer_count = serializers.IntegerField(help_text='最多服务客户数')
    refuse_reason = serializers.CharField(help_text='拒绝原因')
    project_offer_customer_count = serializers.IntegerField(source='project_offer.customer_count',
                                                            help_text='项目offer最大服务客户数')
    settlement_price = serializers.SerializerMethodField(help_text='结算金额信息')

    class Meta:
        model = CoachOffer
        fields = ('id', 'remain_time', 'project_info', 'price', 'status', 'max_customer_count', 'refuse_reason',
                  'project_offer_customer_count', 'settlement_price')

    def get_remain_time(self, obj):
        if obj.status == CoachOfferStatusEnum.not_confirm:
            now = datetime.datetime.now()
            confirm_time = obj.project_offer.confirm_time
            max_seconds = (confirm_time - now).seconds
            days = (confirm_time - now).days
            if max_seconds == 0:
                return f"距离邀请失效还剩余：{days}天0小时"
            hours = max_seconds / 3600
            # last = str(hours).split('.')[-1]
            # if last == '0':
            #     return f"距离邀请失效还剩余：{days}天{int(hours)}时"
            z_hours = int(round(max_seconds / 3600, 0))
            if z_hours > hours:
                z_hours = z_hours - 1
            return f"距离邀请失效还剩余：{days}天{z_hours}小时"
            # seconds = (hours - z_hours) * 3600
            #
            # minute = seconds / 60
            # last = str(minute).split('.')[-1]
            # if last == '0':
            #     return f"距离邀请失效还剩余：{days}天{z_hours}时{int(minute)}分0秒"
            # z_minute = int(round(seconds / 60, 0))
            # if z_minute > minute:
            #     z_minute = z_minute - 1
            # second = (minute - z_minute) * 60
            # second = int(round(second, 0))
            # return f"距离邀请失效还剩余：{days}天{z_hours}时{z_minute}分{second}秒"

    def get_project_info(self, obj):
        data = {"project_name": None, "project_cycle": None, "interview_time": None,
                "personal_target": None}
        project = obj.project_offer.project
        project_name = project.full_name
        data['project_name'] = project_name
        data['personal_target'] = obj.project_offer.project_msg
        if project.start_time and project.end_time:
            project_cycle = f"{project.start_time.strftime('%Y.%m.%d')} ~ {project.end_time.strftime('%Y.%m.%d')} " \
                            f"(预估{(project.end_time - project.start_time).days + 1}天)"
            data['project_cycle'] = project_cycle
        customer_portrait = project.project_customer_portrait.filter(deleted=False)
        if customer_portrait.filter(type=CustomerPortraitTypeEnum.group).exists():
            customer_portrait = customer_portrait.filter(type=CustomerPortraitTypeEnum.group).first()
        elif customer_portrait.filter(type=CustomerPortraitTypeEnum.personal).exists():
            customer_portrait = customer_portrait.filter(type=CustomerPortraitTypeEnum.personal).first()
        
        if customer_portrait:
            interview_time = f"{customer_portrait.expected_interview_start_time.strftime('%Y.%m.%d')} ~ " \
                             f"{customer_portrait.expected_interview_end_time.strftime('%Y.%m.%d')}" if \
                customer_portrait.expected_interview_start_time and customer_portrait.expected_interview_end_time else None
            data['interview_time'] = interview_time
        
        # Add settlement price information
        data['settlement_info'] = obj.project_offer.get_settlement_description
        return data

    def get_settlement_price(self, obj):
        return obj.project_offer.get_settlement_price


class AppCoachOfferUpdateSerializer(serializers.Serializer):
    offer_id = serializers.CharField(required=True, write_only=True)
    max_customer_count = serializers.IntegerField(required=False, write_only=True, help_text='最大服务客户数')
    status = serializers.IntegerField(required=True, write_only=True, help_text='状态')
    refuse_reason = serializers.CharField(required=False, write_only=True, help_text='拒绝原因')

    def update_offer(self, validated_data):
        offer_id = validated_data.pop('offer_id')
        coach_offer = CoachOffer.objects.get(id=offer_id)
        status = validated_data.get('status')

        with transaction.atomic():
            if status == CoachOfferStatusEnum.joined:
                coach_offer.status = CoachOfferStatusEnum.joined
                coach_offer.confirm_time = datetime.datetime.now()
                coach_offer.max_customer_count = validated_data.get('max_customer_count')
                coach_offer.save()
                coach = coach_offer.coach
                resume_ids = list(coach.resumes.filter(deleted=False).order_by('is_customization').values_list('id', flat=True))

                # 项目关联教练
                state, content = project_coach_public.project_add_coach(
                    coach_offer.project_offer.project_id, coach.id, resume_ids, source_type=CoachSourceTypeEnum.offer)
                if not state:
                    raise WisdomValidationError(content)
                send_lark_message.apply_async(
                    kwargs=dict(ids=[coach_offer.id], message_type='coach_offer_add', project_id=coach_offer.project_offer.project_id,
                                sender_id=coach_offer.coach.user.id), countdown=3, expires=10)
            elif status == CoachOfferStatusEnum.rejected:
                coach_offer.status = CoachOfferStatusEnum.rejected
                coach_offer.refuse_reason = validated_data.get('refuse_reason')
                coach_offer.confirm_time = datetime.datetime.now()
                coach_offer.save()
                send_lark_message.apply_async(
                    kwargs=dict(ids=[coach_offer.id], message_type='coach_offer_rejected', project_id=coach_offer.project_offer.project_id,
                                sender_id=coach_offer.coach.user.id), countdown=3, expires=10)
        data = AppCoachOfferSerializer(coach_offer).data
        return data
