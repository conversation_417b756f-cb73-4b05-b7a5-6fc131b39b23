import datetime
import uuid
import pendulum
import redis
import logging
from django.conf import settings
from celery import shared_task
from celery.exceptions import SoftTimeLimitExceeded

from wisdom_v2.common import change_observation_public
from utils.message.lark_message import LarkMessageCenter
from utils.feishu_robot import push_wx_error_message
from utils.qr_code import get_base_qr_code, get_qr_code
from utils.messagecenter.getui import send_work_wechat_coach_notice, send_work_wechat_coachee_notice
from utils.messagecenter.sms import SendSMS
from utils.send_account_email import get_project_manage_wx_user_id
from utils.message.email import message_send_email_base
from utils.wechat_oauth import WeChatMiniProgram
from utils import aesdecrypt
from utils.feishu_robot import push_celery_hanging_message
from wisdom_v2.enum.service_content_enum import NoticeChannelTypeEnum, NoticeTemplateTypeEnum
from wisdom_v2.enum.project_interview_enum import ProjectInterviewPlaceCategoryEnum, ProjectInterviewTypeEnum
from wisdom_v2.enum.user_enum import UserRoleEnum
from wisdom_v2.enum.chemical_interview_enum import ChemicalInterviewCoachSourceEnum, ChemicalInterviewStatusEnum
from wisdom_v2.models import UserNoticeRecord, WorkWechatUser, ProjectCoach, ProjectMember, UserBackend, \
    ProjectInterested, MultipleAssociationRelation, ChangeObservation, CoachTask, TotalTemplate
from wisdom_v2.models_file import ChemicalInterviewModule, StakeholderInterview, StakeholderInterviewModule
from wisdom_v2.models_file.project_service import ProjectServiceContent
from wisdom_v2.enum.project_interview_enum import DataType
from wisdom_v2.views import constant
from wisdom_v2.common import project_public, project_service_public, resume_public
from wisdom_v2 import utils
from django.db.models import Sum

from wisdom_v2.views.constant import ATTR_STATUS_INTERVIEW_CANCEL
from wisdom_v2.models import ProjectInterview, User
from wisdom_v2.models_file import GrowthGoalsModule
from utils.messagecenter.center import push_v2_message




data_redis = redis.Redis.from_url(settings.DATA_REDIS)
api_action_logging = logging.getLogger('api_action')


def send_interview_notify(project_interview_id):
    """
    如果项目配置了成长目标，且该用户累计辅导时长达到设定的小时数要求，就发送特殊版本成长目标约谈提醒邮件，或者阶段总结约谈提醒邮件
    :param project_interview_id: 约谈id
    """
    try:
        # 获取约谈信息
        project_interview = ProjectInterview.objects.filter(
            id=project_interview_id,
            deleted=False
        ).first()

        if not project_interview:
            return

        # 当前时间超过了项目的开始结束日期，也不发送
        if project_interview.public_attr.project.end_time < datetime.date.today() or project_interview.public_attr.project.start_time > datetime.date.today():
            return


        # 获取项目和成员信息
        project = project_interview.public_attr.project
        target_user = project_interview.public_attr.target_user  # 被教练者
        coach_user = project_interview.public_attr.user  # 教练
        interview_time = project_interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + \
                            '-' + project_interview.public_attr.end_time.strftime('%H:%M')

        # 计算该用户在该项目中的总辅导时长（分钟）
        interviews = ProjectInterview.objects.filter(
            public_attr__project=project,
            public_attr__target_user=target_user,
            public_attr__end_time__lte=project_interview.public_attr.end_time,  # 包括本次辅导
            type=ProjectInterviewTypeEnum.formal_interview.value,
            deleted=False,
            times__isnull=False
        ).exclude(public_attr__status=ATTR_STATUS_INTERVIEW_CANCEL).aggregate(used_times=Sum('times'))
        total_minutes = interviews.get('used_times', 0) if interviews.get('used_times', 0) else 0  # 排除已取消的

        # 转换为小时
        total_hours = total_minutes / 60.0

        is_growth_email = False
        # 先检查项目是否配置了成长目标
        # 查找该用户在该项目中的成长目标配置
        growth_goals_modules = GrowthGoalsModule.objects.filter(
            project_bundle__project=project,
            project_bundle__project_member__user=target_user,
            deleted=False
        )

        if growth_goals_modules.exists():
            # 检查是否有成长目标模块的小时数要求已达到
            for growth_module in growth_goals_modules:
                if growth_module.hours and total_hours == growth_module.hours:
                    is_growth_email = True

        if is_growth_email:

            # 有就发送邮件 - 发送特殊版本的成长目标约谈提醒邮件
            # 发送给教练的成长目标提醒邮件
            push_v2_message.delay(
                coach_user,
                'interview_add_growth_goals_coach',
                param={
                    'interview_time': interview_time,
                    'target_user': target_user.cover_name,
                    'project_name': project.full_name
                },
                project_id=project.id
            )

            # 发送给被教练者的成长目标提醒邮件
            push_v2_message.delay(
                target_user,
                'interview_add_growth_goals',
                param={
                    'interview_time': interview_time,
                    'target_user': coach_user.cover_name
                },
                project_id=project.id
            )

            # 发送给企业管理员，密送运营
            admin_list = project.admin_list
            manager_list = project.manager_list
            if admin_list:
                push_v2_message.delay(
                    User.objects.get(id=admin_list[0]['user_id']),
                    'interview_add_growth_goals_manage',
                    param={
                        'interview_time': interview_time,
                        'coachee_name': target_user.cover_name,
                        'coach_name': coach_user.cover_name,
                    },
                    project_id=project.id,
                    copy_email=[manager_list[0]['email']] if manager_list else None
                )
        else:
            # 普通约谈提醒邮件
            push_v2_message.delay(coach_user, 'interview_add', param={'interview_time': interview_time,
                                                                    'target_user': target_user.cover_name}, project_id=project.id)
            push_v2_message.delay(target_user, 'interview_add', param={'interview_time': interview_time,
                                                                'target_user': coach_user.cover_name}, project_id=project.id)

    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_interview_notify:{str(e)}'})



@shared_task(queue='default', ignore_result=True)
def send_coach_minaapp_resume_email(resume_id, user):
    try:
        # 获取教练信息
        qrcode_state, content = WeChatMiniProgram().get_miniapp_qrcode(scene=f'resume_id={resume_id}&refer=2')
        if not qrcode_state:
            push_wx_error_message(name='获取微信小程序教练简历接口错误', level='error', content={
                'resume_id': resume_id,
                'content': content
            })
            resume_url = None
        else:
            resume_url = get_base_qr_code(content, 'coach_resume')

        url_state, short_link = WeChatMiniProgram().get_url_link('pages/user/myResume', f'resume_id={resume_id}&refer=2')
        if not url_state:
            push_wx_error_message(name='获取微信小程序教练简历接口错误', level='error', content={
                'resume_id': resume_id,
                'content': short_link
            })
            short_link = ''

        params = {"true_name": user.cover_name, "email": user.email, 'short_link': short_link,
                  "password": aesdecrypt(user.password), "resume_url": resume_url}
        message_send_email_base('remind_resume_email', params, [user.email], receiver_ids=user.id)
        return
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_coach_minaapp_resume_email')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_coach_minaapp_resume_email:{str(e)}'})
        

@shared_task(queue='default', ignore_result=True)
def send_user_notify(template_type, channel_type, data_ids, key, sender_id=None):
    try:

        # template_type 发送模板类型 1-账号开通通知 2-改变观察问卷 3-利益相关者调研问卷
        # channel_type 发送渠道 1-企业微信 2-短信 3-邮件
        errmsg = ['企业微信', '短信', '邮件']

        err = []
        # 账号开通通知
        if template_type == NoticeTemplateTypeEnum.create_account.value:
            # 邮件通知
            if channel_type == constant.ADMIN_SEND_EMAIL:
                for item in data_ids:
                    project_member = ProjectMember.objects.get(id=item)
                    msg = utils.send_project_user_add_notice(project_member, sender_id=sender_id)
                    if msg:
                        err.append(msg)
        # MOP被教练者账号开通通知
        elif template_type == NoticeTemplateTypeEnum.create_account_mop:
            if channel_type == constant.ADMIN_SEND_EMAIL:
                for item in data_ids:
                    project_member = ProjectMember.objects.get(id=item)
                    msg = utils.send_project_user_add_notice(project_member, project_type=2, sender_id=sender_id)
                    if msg:
                        err.append(msg)
        # 企业管理员账号开通通知
        elif template_type == NoticeTemplateTypeEnum.company_manage_create_account:
            if channel_type == constant.ADMIN_SEND_EMAIL:
                for item in data_ids:
                    user_backend = UserBackend.objects.get(id=item)
                    msg = utils.send_company_manage_add_notice(user_backend, sender_id=sender_id)
                    if msg:
                        err.append(msg)
        # 客户信息收集通知
        elif template_type == NoticeTemplateTypeEnum.customer_infomation_collection.value:
            if channel_type == constant.ADMIN_SEND_EMAIL:
                for item in data_ids:
                    is_success, msg = project_public.send_customer_info_collection_email(item, key, sender_id=sender_id)
                    if not is_success:
                        err.append(msg)
        # 化学面谈日程设置提醒
        elif template_type == NoticeTemplateTypeEnum.chemical_interview_schedule:
            for item in data_ids:
                project_coach = ProjectCoach.objects.get(pk=item)
                work_wechat_user = WorkWechatUser.objects.filter(user=project_coach.coach.user, deleted=False).first()
                if not work_wechat_user:
                    err.append(project_coach.coach.user.cover_name)
                    continue
                chemical_interview_date = ''
                # 化学面谈起止时间获取项目下第一个配置的化学面谈客户的起止时间，没有时为空
                chemical_interview_module = ChemicalInterviewModule.objects.filter(
                    project_member__project=project_coach.project, deleted=False).first()
                if chemical_interview_module:
                    chemical_interview_date = f"{chemical_interview_module.start_time.strftime('%Y.%m.%d')}-" \
                                              f"{chemical_interview_module.end_time.strftime('%Y.%m.%d')}"

                chemical_interview_time = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y.%m.%d %H:%M')
                content_is_success, app_is_success = send_work_wechat_coach_notice(
                    work_wechat_user.wx_user_id, 'coach_chemical_interview_schedule',
                    project_id=project_coach.project.id, sender_id=sender_id,
                    coach_id=work_wechat_user.user.id, project_name=project_coach.project.full_name,
                    chemical_interview_date=chemical_interview_date, chemical_interview_time=chemical_interview_time)
                if content_is_success and app_is_success:
                    UserNoticeRecord.objects.create(project=project_coach.project, user=project_coach.coach.user,
                                                    type=NoticeTemplateTypeEnum.chemical_interview_schedule,
                                                    channel=constant.ADMIN_SEND_WORK_WECHAT)
                else:
                    err.append(project_coach.coach.user.cover_name)
        # 化学面谈预约提醒 企微+邮件
        elif template_type == NoticeTemplateTypeEnum.chemical_interview_appointment:
            for item in data_ids:
                project_member = ProjectMember.objects.get(pk=item)
                chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()
                if not chemical_interview_module:
                    err.append(project_member.user.cover_name)
                    continue
                interview_count = chemical_interview_module.max_interview_number
                if chemical_interview_module.coach_source == ChemicalInterviewCoachSourceEnum.system_random:
                    coach_count = chemical_interview_module.coaches.filter(deleted=False, coach__isnull=False).count()
                else:
                    coach_count = ProjectCoach.objects.filter(project=project_member.project, deleted=False,
                                                        coach__deleted=False, resume__isnull=False).count()
                duration = chemical_interview_module.duration
                if channel_type == constant.ADMIN_SEND_EMAIL:
                    # 生成短链接
                    url_state, app_short_link = WeChatMiniProgram().get_url_link(
                        'pages_account/login/login',f'refer=2')

                    message_type = 'chemical_interview_appointment'
                    manage_list = project_member.project.manager_list

                    short_link = f'https://static.qzcoach.com/app/miniapp_qrcode.html?qrcode=https://static.qzcoach.com/app/b_login_qrcode.jpg&shortlink={app_short_link}'
                    params = {
                        "true_name": project_member.user.cover_name,
                        "count": coach_count,
                        "account": project_member.user.email,
                        "password": aesdecrypt(project_member.user.password),
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "manager_phone": manage_list[0]['phone'] if manage_list else '',
                        "short_link": short_link,
                        "interview_count": interview_count,
                        "duration": duration
                    }
                    email = [project_member.user.email]
                    attachments = [
                        ('小程序使用指南.pdf', open('intro_chemical.pptx', 'rb').read(), 'application/pdf')
                    ]
                    state = message_send_email_base(message_type=message_type, params=params, to_email=email,
                                                    attachments=attachments,
                                                    project_id=project_member.project_id, receiver_ids=project_member.user_id,
                                                    sender_id=sender_id)
                    if state:
                        UserNoticeRecord.objects.create(project=project_member.project, user=project_member.user,
                                                        type=NoticeTemplateTypeEnum.chemical_interview_appointment,
                                                        channel=constant.ADMIN_SEND_EMAIL)
                    else:
                        err.append(project_member.user.cover_name)
                elif channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    msg, sender, external_user_id = get_project_manage_wx_user_id(
                        project_member.project.pk,
                        project_member.user_id,
                        'send_user_notify'
                    )
                    if not msg:
                        err.append(project_member.user.cover_name)
                        continue
                    sender_user = WorkWechatUser.objects.filter(
                        wx_user_id=sender,
                        user__isnull=False,
                        deleted=False
                    ).first()
                    if sender_user:
                        text = f'我们为您推荐了{coach_count}位匹配度较高的教练，请优先选择一位您最感兴趣的教练进行化学面谈吧！' \
                               f'如果您还未登录过小程序，请移步至您的企业邮箱查收账号密码。'
                        page = "pages_account/login/login?refer=2"   # 跳转首页
                        title = "预约化学面谈"

                        state, msg = send_work_wechat_coachee_notice(
                            sender, text, external_user_id, page, title,
                            content_type='add_msg_template',
                            project_id=project_member.project.id,
                            project_name=project_member.project.name,
                            coachee_id=project_member.user_id,
                            coachee_name=project_member.user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                        )
                        if not state:
                            err.append(project_member.user.cover_name)
                        else:
                            UserNoticeRecord.objects.create(project=project_member.project, user=project_member.user,
                                                            type=NoticeTemplateTypeEnum.chemical_interview_appointment,
                                                            channel=constant.ADMIN_SEND_WORK_WECHAT)
        # 化学面谈反馈提醒 企微 + 邮件
        elif template_type == NoticeTemplateTypeEnum.chemical_interview_feedback:
            for item in data_ids:
                project_member = ProjectMember.objects.get(pk=item)
                chemical_interview_module = project_member.chemical_interview.filter(deleted=False).first()
                if not chemical_interview_module:
                    err.append(project_member.user.cover_name)
                    continue
                chemical_interview = chemical_interview_module.coaches.filter(
                    deleted=False, interview__public_attr__end_time__lte=datetime.datetime.now(),
                    chemical_interview_status=ChemicalInterviewStatusEnum.not_feedback)
                if not chemical_interview.exists():
                    err.append(f"{project_member.user.cover_name}(没有可发送的面谈)")
                    err.append(project_member.user.cover_name)
                    continue
                if channel_type == constant.ADMIN_SEND_EMAIL:
                    # 生成短链接
                    url_state, short_link = WeChatMiniProgram().get_url_link(
                        'pages_interview/feedback_interview/feedback_interview',
                        f'interview_id={chemical_interview.first().interview_id}&refer=2')
                    message_type = 'chemical_interview_feedback'
                    manage_list = project_member.project.manager_list
                    state, content = WeChatMiniProgram().get_miniapp_qrcode(
                        scene=f"interview_id={chemical_interview.first().interview_id}&refer=2",
                        page='pages_interview/feedback_interview/feedback_interview')
                    if not state:
                        err.append(project_member.user.cover_name)
                        continue
                    url = get_base_qr_code(content, 'coach_resume')

                    params = {
                        "true_name": project_member.user.cover_name,
                        "coach_name": chemical_interview.first().interview.public_attr.user.cover_name,
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "manager_phone": manage_list[0]['phone'] if manage_list else '',
                        "qr_code_url": url,
                        "short_link": short_link
                    }
                    email = [project_member.user.email]
                    state = message_send_email_base(message_type=message_type, params=params, to_email=email,
                                                    project_id=project_member.project_id, receiver_ids=project_member.user_id,
                                                    sender_id=sender_id)
                    if state:
                        UserNoticeRecord.objects.create(project=project_member.project, user=project_member.user,
                                                        type=NoticeTemplateTypeEnum.chemical_interview_feedback,
                                                        channel=constant.ADMIN_SEND_EMAIL)
                    else:
                        err.append(project_member.user.cover_name)
                elif channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    msg, sender, external_user_id = get_project_manage_wx_user_id(
                        project_member.project.pk,
                        project_member.user_id,
                        'send_user_notify'
                    )
                    if not msg:
                        err.append(project_member.user.cover_name)
                        continue
                    sender_user = WorkWechatUser.objects.filter(
                        wx_user_id=sender,
                        user__isnull=False,
                        deleted=False
                    ).first()
                    if sender_user:

                        text = f"您和{chemical_interview.first().public_attr.user.cover_name}教练的化学面谈已结束，" \
                               f"请点击下方小程序链接反馈您是否选择该教练吧"

                        page = f"pages_interview/feedback_interview/feedback_interview?" \
                               f"interview_id={chemical_interview.first().interview_id}&refer=2"   # 访谈反馈结果界面
                        title = "访谈结果反馈"

                        state, msg = send_work_wechat_coachee_notice(
                            sender, text, external_user_id, page, title,
                            content_type='add_msg_template',
                            project_id=project_member.project.id,
                            project_name=project_member.project.name,
                            coachee_id=project_member.user_id,
                            coachee_name=project_member.user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                        )
                        if not state:
                            err.append(project_member.user.cover_name)
                        else:
                            UserNoticeRecord.objects.create(project=project_member.project, user=project_member.user,
                                                            type=NoticeTemplateTypeEnum.chemical_interview_feedback,
                                                            channel=constant.ADMIN_SEND_WORK_WECHAT)

        # 改变观察问卷
        elif template_type == NoticeTemplateTypeEnum.change_observation:

            raw_data = {}
            interested = ProjectInterested.objects.filter(pk__in=data_ids)
            for item in interested:
                # 通过关系表获取所有改变观察反馈
                query_data = MultipleAssociationRelation.objects.filter(
                    secondary_id=item.id,
                    deleted=False,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_change_observation,
                ).values_list('main_id', flat=True)
                if not query_data:
                    err.append(item.interested.cover_name)
                    continue
                # 未完成的发送通知
                query_change_observation = ChangeObservation.objects.filter(
                    pk__in=query_data,
                    deleted=False,
                    is_complete=False
                )
                if query_change_observation.exists():
                    for change_observation in query_change_observation.all():
                        if raw_data.get(item.interested.id):
                            raw_data[item.interested.id]['change_observation'].append(change_observation)
                            raw_data[item.interested.id]['interested'].append(item)
                        else:
                            raw_data[item.interested.id] = {
                                'change_observation': [change_observation],
                                'interested': [item]
                            }
                else:
                    err.append(item.interested.cover_name)

            for k, v in raw_data.items():
                interested = v.get('interested')
                change_observation = v.get('change_observation')
                # 短信通知
                if channel_type == constant.ADMIN_SEND_SMS:
                    msg = change_observation_public.send_change_observation_sms_notice(
                        [interested[0]], change_observation[0],
                        all_change_observation=change_observation,
                        all_interested=interested,
                        sender_id=sender_id
                    )
                # 邮件通知
                elif channel_type == constant.ADMIN_SEND_EMAIL:
                    msg = change_observation_public.send_change_observation_email_notice(
                        [interested[0]], change_observation[0],
                        all_change_observation=change_observation,
                        all_interested=interested,
                        sender_id=sender_id
                    )
                else:
                    msg = None
                if msg:
                    err.append(*msg)

        # 利益相关者调研
        elif template_type == NoticeTemplateTypeEnum.stakeholder:

            raw_data = {}
            interested = ProjectInterested.objects.filter(pk__in=data_ids)
            for item in interested:
                # 通过关系表获取所有利益相关者调研
                query_data = MultipleAssociationRelation.objects.filter(
                    secondary_id=item.id,
                    deleted=False,
                    public_attr__isnull=True,
                    type=MultipleAssociationRelationTypeEnum.stakeholder_coach_task,
                ).values_list('main_id', flat=True)
                if not query_data:
                    err.append(item.interested.cover_name)
                    continue

                query_coach_task = CoachTask.objects.filter(
                    pk__in=query_data,
                    public_attr__user_id__isnull=False,
                    deleted=False,
                    stakeholder_submit_time__isnull=True
                )
                if query_coach_task.exists():
                    for coach_task in query_coach_task.all():
                        if raw_data.get(item.interested.id):
                            raw_data[item.interested.id]['coach_task'].append(coach_task)
                            raw_data[item.interested.id]['interested'].append(item)
                        else:
                            raw_data[item.interested.id] = {
                                'coach_task': [coach_task],
                                'interested': [item]
                            }
                else:
                    err.append(item.interested.cover_name)
            for k, v in raw_data.items():
                interested = v.get('interested')
                coach_task = v.get('coach_task')
                # 短信通知
                if channel_type == constant.ADMIN_SEND_SMS:
                    msg = utils.send_stakeholder_coach_task_sms_notice(
                        [interested[0]], coach_task[0], all_coach_task=coach_task, all_interested=interested, sender_id=sender_id
                    )
                # 邮件通知
                elif channel_type == constant.ADMIN_SEND_EMAIL:
                    msg = utils.send_stakeholder_coach_task_email_notice(
                        [interested[0]], coach_task[0], all_coach_task=coach_task, all_interested=interested, sender_id=sender_id)
                else:
                    msg = None
                if msg:
                    err.append(*msg)

        # 利益相关者访谈预约提醒
        elif template_type == NoticeTemplateTypeEnum.stakeholder_interview_reservation:
            data_ids = [uuid.UUID(item).hex for item in data_ids]

            # 查询利益相关者关联表
            stakeholder_interview = StakeholderInterview.objects.filter(id__in=data_ids, deleted=False)

            for item in stakeholder_interview.all():

                project_member = item.stakeholder_interview_module.project_member

                path = 'pages_stakeholder/add_stakeholder_interview/add_stakeholder_interview'
                scene = f'id={item.id}&refer=2'
                send_notice_state = False
                if channel_type == constant.ADMIN_SEND_SMS and item.project_interested.interested.phone:
                    if item.project_interested.interested.phone:
                        sms_scene = f'id={item.id}&invite_code={settings.SMS_INVITE_CODE}&refer=3'
                        state, url = WeChatMiniProgram().get_url_link('pages/landing/landing', f'p=/{path}&{sms_scene}')
                        if state:
                            msg = SendSMS().send_stakeholder_interview_reservation(
                                project_member.user.cover_name,
                                item.project_interested.interested.phone,
                                url,
                                project_id=project_member.project_id,
                                sender_id=sender_id,
                                receiver_id=item.project_interested.interested.id
                            )
                            if msg.get('status') == 'success':
                                send_notice_state = True

                elif channel_type == constant.ADMIN_SEND_EMAIL and item.project_interested.interested.email:
                    manage_list = project_member.project.manager_list
                    url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{path}&{scene}')
                    # 生成短链接
                    url_state, short_link = WeChatMiniProgram().get_url_link(path, scene)

                    message_type = 'stakeholder_interview_reservation'
                    params = {
                        'stakeholder_name': item.project_interested.interested.cover_name,
                        'coachee_name': item.project_interested.master.cover_name,
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "manager_phone": manage_list[0]['phone'] if manage_list else '',
                        "qr_code_url": url,
                        "short_link": short_link
                    }
                    to_email = [item.project_interested.interested.email]
                    state = message_send_email_base(message_type, params, to_email,
                                                    project_id=project_member.project_id, receiver_ids=item.project_interested.interested_id,
                                                    sender_id=sender_id)
                    if state:
                        send_notice_state = True

                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=project_member.project, user=item.project_interested.interested,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.project_interested.interested.cover_name)

        # 客户邀请利益相关者预约访谈提醒
        elif template_type == NoticeTemplateTypeEnum.coachee_invite_stakeholder_interview_reservation:
            # 查询利益相关者配置表
            data_ids = [uuid.UUID(item).hex for item in data_ids]
            stakeholder_interview = StakeholderInterviewModule.objects.filter(id__in=data_ids, deleted=False)

            for item in stakeholder_interview.all():
                page = 'pages_stakeholder/inviting_interviews/inviting_interviews'
                scene = f'id={item.pk}&refer=2'
                send_notice_state = False
                if channel_type == constant.ADMIN_SEND_EMAIL and item.project_member.user.email:
                    # 生成短链接
                    url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)

                    manage_list = item.project_member.project.manager_list
                    url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
                    message_type = 'coachee_invite_stakeholder_interview_reservation'
                    params = {
                        'coachee_name': item.project_member.user.cover_name,
                        'interview_number': item.stakeholder_interview_number,
                        'duration': int(item.duration * 60) if item.duration else '',
                        'datetime': pendulum.now().add(days=1).strftime("%Y.%m.%d %H:%M"),
                        'password': aesdecrypt(item.project_member.user.password),
                        'user_name': item.project_member.user.email,  # 登录名使用邮箱
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "manager_phone": manage_list[0]['phone'] if manage_list else '',
                        "qr_code_url": url,
                        "short_link": short_link
                    }
                    to_email = [item.project_member.user.email]
                    state = message_send_email_base(message_type, params, to_email, project_id=item.project_member.project_id,
                                                   receiver_ids=item.project_member.user_id, sender_id=sender_id)
                    if state:
                        send_notice_state = True
                elif channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    msg, sender, external_user_id = get_project_manage_wx_user_id(
                        item.project_member.project.pk, item.project_member.user_id, 'coachee_invite_stakeholder_interview_reservation'
                    )
                    if msg:
                        sender_user = WorkWechatUser.objects.filter(
                            wx_user_id=sender, user__isnull=False, deleted=False).first()

                        text = f"为了让您在后续的教练过程中获得良好的学习体验，" \
                               f"请您邀请{item.stakeholder_interview_number}位您的利益相关方（您的上下平级同事）参与教练访谈，以便教练更好的了解您。"

                        page = f"{page}?{scene}"
                        title = "邀请利益相关者预约访谈"

                        state, msg = send_work_wechat_coachee_notice(
                            sender, text, external_user_id, page, title,
                            project_id=item.project_member.project.id,
                            project_name=item.project_member.project.name,
                            coachee_id=item.project_member.user_id,
                            coachee_name=item.project_member.user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id,
                            file_name='stakeholder_share.png',
                            sender_id=sender_user.user.id,
                            receiver_id=item.project_member.user_id
                        )
                        if state:
                            send_notice_state = True

                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=item.project_member.project, user=item.project_member.user,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.project_member.user.cover_name)

        # 利益相关者访谈可预约日程设置提醒
        elif template_type == NoticeTemplateTypeEnum.stakeholder_interview_schedule:
            data_ids = [uuid.UUID(item).hex for item in data_ids]
            stakeholder_interview = StakeholderInterviewModule.objects.filter(
                id__in=data_ids, deleted=False, coach_task__coachee_submit_time__isnull=True)
            for item in stakeholder_interview.all():

                send_notice_state = False

                if channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    coach_work_wechat_user = WorkWechatUser.objects.filter(
                        user=item.coach_task.public_attr.user,
                        wx_user_id__isnull=False,
                        deleted=False,
                    ).first()
                    if coach_work_wechat_user:
                        content_is_success, app_is_success = send_work_wechat_coach_notice(
                            coach_work_wechat_user.wx_user_id,
                            'stakeholder_interview_schedule',
                            project_id=item.project_member.project.id,
                            project_name=item.project_member.project.full_name,
                            end_time=pendulum.now().add(days=1).strftime('%Y.%m.%d %H:%M'),
                            interview_date=f'{item.start_date.strftime("%Y.%m.%d")}-{item.end_date.strftime("%Y.%m.%d")}',
                            coachee_id=item.coach_task.public_attr.target_user_id,
                            coachee_name=item.coach_task.public_attr.target_user.cover_name,
                            coach_name=item.coach_task.public_attr.user.cover_name,
                            coach_id=item.coach_task.public_attr.user.id,
                            sender_id=sender_id
                        )
                        if all([content_is_success, app_is_success]):
                            send_notice_state = True
                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=item.project_member.project, user=item.coach_task.public_attr.user,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.coach_task.public_attr.user.cover_name)

        # 利益相关者访谈报告填写提醒
        elif template_type == NoticeTemplateTypeEnum.stakeholder_interview_fill_in_report:
            data_ids = [uuid.UUID(item).hex for item in data_ids]
            # 查询利益相关者配置表
            stakeholder_interview = StakeholderInterviewModule.objects.filter(
                id__in=data_ids, deleted=False, coach_task__coachee_submit_time__isnull=True)

            for item in stakeholder_interview.all():
                send_notice_state = False

                if channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    coach_work_wechat_user = WorkWechatUser.objects.filter(
                        user=item.coach_task.public_attr.user,
                        wx_user_id__isnull=False,
                        deleted=False,
                    ).first()
                    if coach_work_wechat_user:
                        content_item = [
                            {"key": "客户名称", "value": item.coach_task.public_attr.target_user.cover_name},
                            {"key": "所属企业", "value": item.coach_task.public_attr.project.company.real_name},
                            {"key": "所属项目", "value": item.coach_task.public_attr.project.name},
                        ]
                        content_is_success, app_is_success = send_work_wechat_coach_notice(
                            coach_work_wechat_user.wx_user_id,
                            'stakeholder_interview_record_fill_notice',
                            project_id=item.project_member.project.id,
                            content_item=content_item,
                            project_name=item.project_member.project.full_name,
                            coach_task_id=item.coach_task_id,
                            coachee_id=item.coach_task.public_attr.target_user_id,
                            coachee_name=item.coach_task.public_attr.target_user.cover_name,
                            coach_name=item.coach_task.public_attr.user.cover_name,
                            coach_id=item.coach_task.public_attr.user.id,
                            sender_id=sender_id
                        )
                        if all([content_is_success, app_is_success]):
                            send_notice_state = True
                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=item.project_member.project, user=item.coach_task.public_attr.user,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.coach_task.public_attr.user.cover_name)

        # 改变观察反馈客户邀请利益相关者
        elif template_type == NoticeTemplateTypeEnum.change_observation_customer.value:
            change_observation = ChangeObservation.objects.filter(id__in=data_ids, deleted=False)
            for item in change_observation.all():

                # 如果没有配置需要的利益相关者数量就跳过并给出错误提示
                if not item.max_stakeholders_count:
                    err.append(item.project_member.user.cover_name)
                    continue

                page = 'pages_change/change_observation/inviting_change_observation'
                scene = f'id={item.pk}&refer=2'
                send_notice_state = False

                coachee_name = item.project_member.user.cover_name
                project_name = item.project_member.project.name
                write_end_date = item.stakeholders_write_end_date.strftime('%Y.%m.%d %H:%M') if item.stakeholders_write_end_date else ''
                invite_end_time = item.invite_end_time.strftime('%Y.%m.%d %H:%M') if item.invite_end_time else ''

                if channel_type == constant.ADMIN_SEND_EMAIL and item.project_member.user.email:
                    # 生成短链接
                    url_state, short_link = WeChatMiniProgram().get_url_link(page, scene)

                    manage_list = item.project_member.project.manager_list
                    url = get_qr_code(f'{settings.SITE_URL}landing/?p=/{page}&{scene}')
                    message_type = 'change_observation_customer'
                    params = {
                        'max_stakeholders_count': item.max_stakeholders_count,
                        'coachee_name': coachee_name,
                        'project_name': project_name,
                        'write_end_date': write_end_date,
                        'invite_end_time': invite_end_time,
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "qr_code_url": url,
                        "short_link": short_link
                    }
                    to_email = [item.project_member.user.email]
                    state = message_send_email_base(message_type, params, to_email, project_id=item.project_member.project_id,
                                                   receiver_ids=item.project_member.user_id, sender_id=sender_id)
                    if state:
                        send_notice_state = True
                elif channel_type == constant.ADMIN_SEND_WORK_WECHAT:
                    msg, sender, external_user_id = get_project_manage_wx_user_id(
                        item.project_member.project.pk, item.project_member.user_id,
                        'change_observation_customer'
                    )
                    if msg:
                        sender_user = WorkWechatUser.objects.filter(
                            wx_user_id=sender, user__isnull=False, deleted=False).first()
                        text = (f"{project_name}改变观察反馈正在进行中，请您在"
                                f"{invite_end_time}前邀请您的利益相关者对这段时间您的行为改变进行反馈调研")
                        page = f"{page}?{scene}"
                        title = "改变观察反馈开始提醒"
                        state, msg = send_work_wechat_coachee_notice(
                            sender, text, external_user_id, page, title,
                            project_id=item.project_member.project.id,
                            project_name=item.project_member.project.name,
                            coachee_id=item.project_member.user_id,
                            coachee_name=item.project_member.user.cover_name,
                            forward_by=sender_user.user.cover_name,
                            forward_by_id=sender_user.user.id
                        )
                        if state:
                            send_notice_state = True
                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=item.project_member.project, user=item.project_member.user,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.project_member.user.cover_name)

        # 改变观察开始通知给客户
        elif template_type == NoticeTemplateTypeEnum.change_observation_begin_coachee.value:
            change_observation = ChangeObservation.objects.filter(id__in=data_ids, deleted=False)
            for item in change_observation.all():
                send_notice_state = False
                if channel_type == constant.ADMIN_SEND_EMAIL and item.project_member.user.email:
                    manage_list = item.project_member.project.manager_list
                    message_type = 'change_observation_begin_coachee'
                    params = {
                        'true_name': item.project_member.user.cover_name,
                        "manager_name": manage_list[0]['true_name'] if manage_list else '',
                        "manager_email": manage_list[0]['email'] if manage_list else '',
                        "manager_phone": manage_list[0]['phone'] if manage_list else '',
                    }
                    to_email = [item.project_member.user.email]
                    state = message_send_email_base(message_type, params, to_email,
                                                   project_id=item.project_member.project_id,
                                                   receiver_ids=item.project_member.user_id,
                                                   sender_id=sender_id)
                    if state:
                        send_notice_state = True

                if send_notice_state:
                    UserNoticeRecord.objects.create(
                        project=item.project_member.project, user=item.project_member.user,
                        type=template_type, channel=channel_type)
                else:
                    err.append(item.project_member.user.cover_name)

        # 改变观察开始通知给HR
        elif template_type == NoticeTemplateTypeEnum.change_observation_begin_hr.value:
            userbackend = UserBackend.objects.filter(id__in=data_ids, deleted=False)
            for item in userbackend.all():
                send_notice_state = False
                if channel_type == constant.ADMIN_SEND_EMAIL:
                    hr_email = item.user.email

                    if hr_email:
                        message_type = 'change_observation_begin_hr'
                        params = {
                            'true_name': item.user.cover_name,
                        }

                        state = message_send_email_base(message_type, params, [hr_email],
                                                        project_id=item.project_id,
                                                        sender_id=sender_id)
                        if state:
                            send_notice_state = True
                            

                if send_notice_state:
                    UserNoticeRecord.objects.create(
                                user=item.user,
                                project=item.project,
                                type=template_type,
                                channel=channel_type)
                            
                else:
                    err.append(f"{item.user.cover_name}(HR邮件发送失败)")

        task_id = data_redis.get(key)
        if not task_id:
            api_action_logging.info({
                'error': f'',
                'name': 'send_user_notify'})
            push_wx_error_message(
                name=f'发送用户通知',
                level='warning', content={
                    'error': f'{key}:异步任务id丢失，请排查redis是否出现故障',
                    'details': f'给{"、".join(data_ids)}的{NoticeTemplateTypeEnum.get_display(template_type)}{errmsg[channel_type - 1]}发送失败'
                })
            return False, '异步任务id丢失'
        if err:
            error = f'给{"、".join(err)}的{errmsg[channel_type - 1]}发送失败'
            data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
            return False, error
        else:
            data_redis.set(task_id.decode(), f'True&发送成功', ex=3600)
            return True, '发送成功'
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('send_user_notify')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'send_user_notify:{str(e)}'})