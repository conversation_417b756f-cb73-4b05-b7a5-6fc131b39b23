from wisdom_v2.models_file.platform_notification import PlatformNotification
from utils.feishu_robot import push_celery_hanging_message
from data.models import AppMessagetemplates
from utils.message.lark_message import LarkMessageCenter
from wisdom_v2.enum.service_content_enum import NoticeChannelTypeEnum
from celery import shared_task
from celery.exceptions import SoftTimeLimitExceeded
from utils import null_or_blank
import re
from django.core.mail import EmailMessage

class Email():

    def __init__(self, subject, body, to_email_list, sender=None, copy_email=None, attachments=None):
        self.subject = subject
        self.body = body
        self.email_list = to_email_list
        self.sender = sender
        self.copy_email = copy_email
        self.attachments = attachments

    def send(self):
        if type(self.email_list) is not list:
            return False
        msg = EmailMessage(self.subject, self.body, self.sender, self.email_list, cc=self.copy_email, attachments=self.attachments)
        msg.content_subtype = 'html'
        return msg.send()
    

def replace_sys(content):
    if null_or_blank(content):
        return ''
    sitename = ''
    siteurl = ''
    content = content.replace('$sitename$', sitename)
    content = content.replace('$siteurl$', siteurl)
    return content


def replace_content(content, param):
    if null_or_blank(content):
        return ''
    if param:
        for k,v in param.items():
            content = content.replace('$'+k+'$', str(v))
    content = replace_sys(content)
    content = re.sub('\$\w+\$', '', content)
    return content


# 邮件发送公共接口，拼装邮件内容，发送邮件
# receiver_ids 可以是List或者int
@shared_task(queue='task', ignore_result=True, soft_time_limit=20)
def message_send_email_base(message_type, params, to_email, copy_email=None, attachments=None,
                           project_id=None, sender_id=None, receiver_ids=None):
    try:
        template = AppMessagetemplates.objects.filter(MessageType=message_type).first()
        if not template:
            return None
        subject = replace_content(template.EmailSubject, params)
        body = replace_content(template.EmailBody, params)

        email = Email(subject, body, to_email, copy_email=copy_email, attachments=attachments)
        try:
            state = email.send()

            # 记录通知信息，receiver_ids可能是List或者int
            if isinstance(receiver_ids, list):
                for receiver_id, index in receiver_ids:
                    PlatformNotification.create_notification(
                        subject=subject,
                        template_name=message_type,
                        channel=NoticeChannelTypeEnum.email.value,
                        project_id=project_id,
                        sender_id=sender_id,
                        receiver_id=receiver_id,
                        receiver_contact=to_email[index],
                        status=1 if state else 2
                    )
            else:
                PlatformNotification.create_notification(
                    subject=subject,
                    template_name=message_type,
                    channel=NoticeChannelTypeEnum.email.value,
                    project_id=project_id,
                    sender_id=sender_id,
                    receiver_id=receiver_ids,
                    receiver_contact=to_email[0],
                    status=1 if state else 2
                )

            if not state:
                LarkMessageCenter().send_other_backend_message('邮件发送失败', 'error',
                                                            f"message_type:{message_type}; "
                                                            f"params: {params}, "
                                                            f"state': {state}")
            return state
        except Exception as e:
            LarkMessageCenter().send_other_backend_message('邮件发送失败', 'error',
                                                        f"message_type:{message_type}; params: {params}, "
                                                        f"msg:{str(e)}")
            return False
    except SoftTimeLimitExceeded:
        push_celery_hanging_message('message_send_email_base')
    except Exception as e:
        LarkMessageCenter().send_other_backend_message(
            'celery任务错误', 'error', {'error': f'message_send_email_base:{str(e)}'})