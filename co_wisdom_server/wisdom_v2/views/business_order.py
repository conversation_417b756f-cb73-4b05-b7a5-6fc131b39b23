from decimal import Decimal

from django.db import transaction
from django.db.models import Q
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import viewsets
from datetime import datetime

from utils.api_response import success_response, parameter_error_response
from utils.pagination import StandardResultsSetPagination
from wisdom_v2.common import business_order_public
from wisdom_v2.enum.business_order_enum import BusinessOrderPayStatusEnum, BusinessOrderSettlementStatusEnum, \
    BusinessOrderTypeEnum, BusinessOrderDataTypeEnum
from wisdom_v2.models import UserBackend
from wisdom_v2.models_file import BusinessOrder, PublicCoursesCoach
from wisdom_v2.models_file.business_order import BusinessOrderExtraCost, BusinessOrder2Object
from wisdom_v2.views import constant
from wisdom_v2.views.business_order_actions import BusinessOrderSerializer, edit_business_order_tax_scale, \
    business_order_update_pay_status, BusinessOrderExtraCostBaseSerializer
from django.db.models import Case, When, F, Value, CharField

class BusinessOrderViewSet(viewsets.ModelViewSet):
    queryset = BusinessOrder.objects.filter(deleted=False)
    serializer_class = BusinessOrderSerializer

    @swagger_auto_schema(
        operation_id='订单列表',
        operation_summary='订单列表',
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_QUERY, description='订单编号', type=openapi.TYPE_STRING),
            openapi.Parameter('type', openapi.IN_QUERY, description='工作类型', type=openapi.TYPE_STRING),
            openapi.Parameter('work_type', openapi.IN_QUERY, description='工作形式',type=openapi.TYPE_STRING),
            openapi.Parameter('pay_status', openapi.IN_QUERY, description='支付状态', type=openapi.TYPE_STRING),
            openapi.Parameter('settlement_status', openapi.IN_QUERY, description='结算状态', type=openapi.TYPE_STRING),
            openapi.Parameter('withdrawal_status', openapi.IN_QUERY, description='提现状态', type=openapi.TYPE_STRING),
            openapi.Parameter('pay_start_date', openapi.IN_QUERY, description='支付开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('pay_end_date	', openapi.IN_QUERY, description='支付结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('settlement_start_date', openapi.IN_QUERY, description='结算开始时间', type=openapi.TYPE_STRING),
            openapi.Parameter('settlement_end_date	', openapi.IN_QUERY, description='结算结束时间', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER),
        ],
        tags=['后台结算管理相关']
    )
    def list(self, request, *args, **kwargs):
        business_order = self.get_queryset()

        user = request.user
        user_backend = UserBackend.objects.filter(user=user, deleted=False).first()
        if not user_backend:
            return parameter_error_response('当前用户无权限')

        # 公开课运营只能看到自己创建的公开课订单
        if user_backend.role_id == constant.PUBLIC_COURSES:
            # 查询到项目运营创建的所有公开课id列表
            public_courses_coach_ids = list(PublicCoursesCoach.objects.filter(
                deleted=False, public_courses__creator=user).values_list('id', flat=True))
            if public_courses_coach_ids:
                # 查询到项目运营创建的公开课对应的订单id列表
                business_order_object_ids = list(BusinessOrder2Object.objects.filter(
                    object_id__in=public_courses_coach_ids, deleted=False,
                    data_type=BusinessOrderDataTypeEnum.public_course.value).values_list('business_order_id', flat=True))
                # 如果有订单则修改business_order的查询，通过id限制范围
                if business_order_object_ids:
                    business_order = business_order.filter(
                        id__in=business_order_object_ids, deleted=False, type=BusinessOrderTypeEnum.public_course.value)
                # 没有则直接返回
                else:
                    return success_response()
            # 没有则直接返回
            else:
                return success_response()

        # 项目运营能看到企业项目下项目运营标记为自己的订单，加上自己创建的公开课订单
        elif user_backend.role_id == constant.PROJECT_OPERATION:
            # 获取项目运营绑定过的项目id列表
            project_ids = list(
                UserBackend.objects.filter(user=user, deleted=False).values_list('project_id', flat=True))

            # 查询到当前账号创建的所有公开课id列表
            public_courses_coach_ids = list(PublicCoursesCoach.objects.filter(
                deleted=False, public_courses__creator=user).values_list('id', flat=True))

            query = Q()

            # 如果有项目ID,添加到查询条件
            if project_ids:
                query |= Q(project_id__in=project_ids)

            # 如果有公开课相关的订单,添加到查询条件
            if public_courses_coach_ids:
                business_order_object_ids = list(BusinessOrder2Object.objects.filter(
                    object_id__in=public_courses_coach_ids, deleted=False,
                    data_type=BusinessOrderDataTypeEnum.public_course.value).values_list('business_order_id',
                                                                                         flat=True))

                if business_order_object_ids:
                    query |= Q(id__in=business_order_object_ids,
                               deleted=False,
                               type=BusinessOrderTypeEnum.public_course.value)

            # 应用查询条件
            if query:
                business_order = business_order.filter(query)
            else:
                # 如果没有任何条件匹配,返回一个空的查询集
                return success_response()


        if request.query_params.get('id'):
            business_order = business_order.filter(id__icontains=request.query_params.get('id'))
        if request.query_params.get('type'):
            business_order = business_order.filter(type__in=request.query_params.get('type').split(','))
        if request.query_params.get('work_type'):
            business_order = business_order.filter(work_type__in=request.query_params.get('work_type').split(','))
        if request.query_params.get('pay_status'):
            business_order = business_order.filter(pay_status__in=request.query_params.get('pay_status').split(','))
        if request.query_params.get('settlement_status'):
            business_order = business_order.filter(settlement_status__in=request.query_params.get('settlement_status').split(','))
        if request.query_params.get('withdrawal_status'):
            business_order = business_order.filter(withdrawal_status=request.query_params.get('withdrawal_status'))
        if request.query_params.get('pay_start_date') and request.query_params.get('pay_end_date'):
            pay_start_date = datetime.strptime(request.query_params.get('pay_start_date'), '%Y-%m-%d').date()
            pay_end_date = datetime.strptime(request.query_params.get('pay_end_date'), '%Y-%m-%d').date()
            business_order = business_order.filter(pay_time__date__range=[pay_start_date, pay_end_date])
        if request.query_params.get('settlement_start_date') and request.query_params.get('settlement_end_date'):
            settlement_start_date = datetime.strptime(request.query_params.get('settlement_start_date'), '%Y-%m-%d').date()
            settlement_end_date = datetime.strptime(request.query_params.get('settlement_end_date'), '%Y-%m-%d').date()
            business_order = business_order.filter(
                business_settlement__settlement_time__date__range=[settlement_start_date, settlement_end_date])
        if request.query_params.get('settlement_id'):
            business_order = business_order.filter(business_settlement_id=request.query_params.get('settlement_id'))
        if request.query_params.get('coach_id'):
            business_order = business_order.filter(coach_id=request.query_params.get('coach_id'),
                                                   pay_status__in=[BusinessOrderPayStatusEnum.paid,
                                                                   BusinessOrderPayStatusEnum.non_payment],
                                                   settlement_status=BusinessOrderSettlementStatusEnum.unsettled)
        if request.query_params.get('coachee_user_id'):
            business_order = business_order.filter(user_id=request.query_params.get('coachee_user_id'))
        if request.query_params.get('coach_user_id'):
            business_order = business_order.filter(coach__user_id=request.query_params.get('coach_user_id'))
        if request.query_params.get('company_id'):
            business_order = business_order.filter(project__company_id=request.query_params.get('company_id'))
        if request.query_params.get('work_start_date') and request.query_params.get('work_end_date'):
            work_start_date = datetime.strptime(request.query_params.get('work_start_date'), '%Y-%m-%d').date()
            work_end_date = datetime.strptime(request.query_params.get('work_end_date'), '%Y-%m-%d').date()
            business_order = business_order.filter(work_start_time__date__range=[work_start_date, work_end_date])
        if request.query_params.get('project_id'):
            business_order = business_order.filter(project_id=request.query_params.get('project_id'))
        # 排序
        # business_order = business_order_public.prepare_business_orders_for_sorting(business_order)

        # 根据提现状态、项目名称和创建时间进行排序
        # sorted_orders = sorted(business_order, key=lambda o: (
        #     o.withdrawal_status,
        #     o.project.name if o.project else '',
        #     # (o.sort_name == '', o.sort_name),  # 有值的项先排序
        #     -o.updated_at.timestamp()  # sorted不可用对datetime使用负号排序，将 datetime 对象转换为时间戳进行倒序排序
        # ))
        
        business_order = business_order.annotate(
            project_name=Case(
                When(project__isnull=False, then=F('project__name')),
                default=Value(''),
                output_field=CharField()
            )
        ).order_by(
            'withdrawal_status',
            'project_name',
            '-updated_at'
        )
        sorted_orders = business_order

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(sorted_orders, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='订单结算信息',
        operation_summary='订单结算信息',
        manual_parameters=[
            openapi.Parameter('order_id	', openapi.IN_QUERY, description='订单id', type=openapi.TYPE_STRING),
        ],
        tags=['后台结算管理相关']
    )
    @action(methods=['get'], detail=False, url_path='settlement_message')
    def settlement_message(self, request, *args, **kwargs):
        try:
            order_id = request.query_params['order_id']
            business_order = BusinessOrder.objects.get(id=order_id, deleted=False)
        except (KeyError, BusinessOrder.DoesNotExist):
            return parameter_error_response()
        data = self.serializer_class(business_order, many=False).data
        return success_response(data)

    @swagger_auto_schema(
        operation_id='修改教练单价/税点',
        operation_summary='修改教练单价/税点',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'order_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='订单id'),
                'coach_price': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练单价'),
                'coach_price_type': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练单价类型'),
                'tax_point': openapi.Schema(type=openapi.TYPE_STRING, description='税点'),
                'remark': openapi.Schema(type=openapi.TYPE_STRING, description='备注'),
            }
        ),
        tags=['后台结算管理相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def update_business_order(self, request, *args, **kwargs):
        try:
            order_id = request.data['id']
            business_order = BusinessOrder.objects.get(id=order_id, deleted=False)
            user = request.user
        except (KeyError, BusinessOrder.DoesNotExist):
            return parameter_error_response()
        # if business_order.type == BusinessOrderTypeEnum.personal:
        #     return parameter_error_response('个人订单不可修改')
        data = request.data.copy()
        business_order = edit_business_order_tax_scale(business_order, data, user)
        data = self.serializer_class(business_order, many=False).data
        return success_response(data)

    @swagger_auto_schema(
        operation_id='标记支付状态',
        operation_summary='标记支付状态',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'ids': openapi.Schema(type=openapi.TYPE_NUMBER, description='订单id列表'),
                'status': openapi.Schema(type=openapi.TYPE_NUMBER, description='教练单价'),
            }
        ),
        tags=['后台结算管理相关']
    )
    @action(methods=['post'], detail=False, url_path='update_pay_status')
    def update_pay_status(self, request, *args, **kwargs):
        try:
            ids = request.data['ids']
            business_order = BusinessOrder.objects.filter(id__in=ids, deleted=False)
            status = int(request.data['status'])
        except (KeyError, ValueError, BusinessOrder.DoesNotExist):
            return parameter_error_response()
        if status not in [BusinessOrderPayStatusEnum.paid, BusinessOrderPayStatusEnum.non_payment]:
            return parameter_error_response('只能标记为已支付、未支付无风险')
        data = business_order_update_pay_status(business_order, status)
        return success_response(data)

    @swagger_auto_schema(
        operation_id='查看订单额外费用信息',
        operation_summary='查看订单额外费用信息',
        manual_parameters=[
            openapi.Parameter('id	', openapi.IN_QUERY, description='订单id', type=openapi.TYPE_STRING),
        ],
        tags=['后台结算管理相关']
    )
    @action(methods=['get'], detail=False, url_path='extra_cost')
    def get_business_order_extra_cost(self, request, *args, **kwargs):
        try:
            order_id = request.query_params['id']
            business_order = BusinessOrder.objects.get(pk=order_id, deleted=False)
        except (KeyError, BusinessOrder.DoesNotExist):
            return parameter_error_response()
        data = BusinessOrderExtraCostBaseSerializer(business_order).data
        return success_response(data)

    @swagger_auto_schema(
        operation_id='创建订单额外费用信息',
        operation_summary='创建订单额外费用信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='订单id'),
                'extra_cost': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, description='额外费用', properties={
                        'describe': openapi.Schema(type=openapi.TYPE_STRING, description='描述'),
                        'cost': openapi.Schema(type=openapi.TYPE_INTEGER, description='费用'),
                        'notes': openapi.Schema(type=openapi.TYPE_STRING, description='备注'),
                        'marking_date': openapi.Schema(type=openapi.TYPE_STRING, description='对应日期',
                                                   format=openapi.FORMAT_DATE),
                    })),
            }
        ),
        tags=['后台结算管理相关']
    )
    @action(methods=['post'], detail=False, url_path='extra_cost/add')
    def add_business_order_extra_cost(self, request, *args, **kwargs):
        try:
            business_order_id = request.data['id']
            extra_cost = request.data['extra_cost']
            business_order = BusinessOrder.objects.get(pk=business_order_id, deleted=False)
        except (Exception, BusinessOrder.DoesNotExist):
            return parameter_error_response()

        if not isinstance(extra_cost, list):
            return parameter_error_response('额外费用参数错误')

        with transaction.atomic():
            add_extra_cost = []
            for item in extra_cost:
                add_extra_cost.append(
                    BusinessOrderExtraCost(
                        business_order=business_order,
                        describe=item.get('describe'),
                        notes=item.get('notes'),
                        cost=int(Decimal(str(item.get('cost'))) * Decimal('100')),
                        marking_date=item.get('marking_date')
                    )
                )
            # 创建对象
            BusinessOrderExtraCost.objects.bulk_create(add_extra_cost)

            # 更新金额
            tax_amount, coach_actual_income = business_order_public.calculation_tax_amount(
                business_order.coach_price, business_order.duration, business_order.tax_point, business_order)
            business_order.tax_amount = tax_amount
            business_order.coach_actual_income = coach_actual_income
            business_order.save()

        data = BusinessOrderExtraCostBaseSerializer(business_order).data
        return success_response(data)

    @swagger_auto_schema(
        operation_id='修改订单额外费用信息',
        operation_summary='修改订单额外费用信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='订单id'),
                'extra_cost': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(
                    type=openapi.TYPE_OBJECT, description='额外费用', properties={
                        'describe': openapi.Schema(type=openapi.TYPE_STRING, description='描述'),
                        'cost': openapi.Schema(type=openapi.TYPE_INTEGER, description='费用'),
                        'notes': openapi.Schema(type=openapi.TYPE_STRING, description='备注'),
                        'marking_date': openapi.Schema(type=openapi.TYPE_STRING, description='对应日期',
                                                   format=openapi.FORMAT_DATE),
                    })),
            }
        ),
        tags=['后台结算管理相关']
    )
    @action(methods=['post'], detail=False, url_path='extra_cost/update')
    def update_business_order_extra_cost(self, request, *args, **kwargs):
        try:
            business_order_id = request.data['id']
            extra_cost = request.data['extra_cost']
            business_order = BusinessOrder.objects.get(pk=business_order_id, deleted=False)
        except (Exception, BusinessOrder.DoesNotExist):
            return parameter_error_response()

        if not isinstance(extra_cost, list):
            return parameter_error_response('额外费用参数错误')

        add_extra_cost_dict = []
        all_extra_cost_id = []
        for extra_cost_item in extra_cost:
            if extra_cost_item.get('id'):
                all_extra_cost_id.append(extra_cost_item.get('id'))
                extra_cost = BusinessOrderExtraCost.objects.filter(id=extra_cost_item.get('id'), deleted=False)
                if extra_cost:
                    extra_cost.update(
                        describe=extra_cost_item.get('describe'),
                        notes=extra_cost_item.get('notes'),
                        cost=int(Decimal(str(extra_cost_item.get('cost'))) * Decimal('100')),
                        marking_date=extra_cost_item.get('marking_date')
                    )
            else:
                extra_cost_item['business_order'] = business_order
                add_extra_cost_dict.append(extra_cost_item)

        # 如果有未存在的订单则删除
        if len(all_extra_cost_id) <= BusinessOrderExtraCost.objects.filter(
                business_order=business_order, deleted=False).count():
            BusinessOrderExtraCost.objects.filter(
                business_order=business_order, deleted=False).exclude(
                id__in=all_extra_cost_id).update(deleted=True)

        # 如果有新增则创建
        if add_extra_cost_dict:
            new_extra_costs = [
                BusinessOrderExtraCost(
                    business_order=item.get('business_order'),
                    describe=item.get('describe'),
                    notes=item.get('notes'),
                    cost=int(Decimal(str(item.get('cost'))) * Decimal('100')),
                    marking_date=item.get('marking_date')
                )
                for item in add_extra_cost_dict
            ]
            BusinessOrderExtraCost.objects.bulk_create(new_extra_costs)

        # 更新金额
        tax_amount, coach_actual_income = business_order_public.calculation_tax_amount(
            business_order.coach_price, business_order.duration, business_order.tax_point, business_order)
        business_order.tax_amount = tax_amount
        business_order.coach_actual_income = coach_actual_income
        business_order.save()

        data = BusinessOrderExtraCostBaseSerializer(business_order).data
        return success_response(data)

