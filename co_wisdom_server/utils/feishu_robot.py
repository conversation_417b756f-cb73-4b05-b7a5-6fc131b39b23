import time
from celery import shared_task
import requests
import datetime
import json
from django.conf import settings
from wisdom_v2.enum.service_content_enum import NoticeChannelTypeEnum
from wisdom_v2.models_file.platform_notification import PlatformNotification
from utils.env_settings import get_settings
from wisdom_v2 import models
from wisdom_v2.common import personal_user_public

def push_celery_hanging_message(func_name):
    env = "测试环境" if "test" in settings.SITE_URL else "正式环境"
    data = {
            "msg_type": "text",
            "content": {
                "text": "[{}]celery执行{}任务超时，请关注worker状态是否健康！".format(env, func_name)
            }
        }
    headers = {"Content-Type": "application/json"}
    requests.request("POST", settings.LARK_CELERY_ROBOT, headers=headers, data=json.dumps(data))


def push_wx_error_message(name, level, content):
    env = get_settings()
    env = env.split('.')[1]
    tmp_dict = {'prod': '正式环境', 'test': '测试环境', 'dev': '开发环境'}
    if env not in tmp_dict.keys():
        return
    env_str = tmp_dict[env]
    time_str = str(time.time()).split('.')[0][-4:]
    num = datetime.datetime.now().strftime("%Y%m%d") + env_str + time_str

    text = """
        {}
        编号：{}
        环境：{}
        级别：{}
        内容：{}
    """.format(name, num, env_str, level, content)
    data = {
        "msg_type": "text",
        "content": {
            "text": text
        }
    }
    headers = {"Content-Type": "application/json"}
    if env in ['prod']:
        requests.request("POST", settings.LARK_ERROR_MESSAGE_ROBOT, headers=headers, data=json.dumps(data))
    else:
        requests.request("POST", settings.LARK_ERROR_MESSAGE_ROBOT_DEV_TEST, headers=headers, data=json.dumps(data))


def push_send_work_wx_message_count(msg):
    env = get_settings()
    env = env.split('.')[1]
    tmp_dict = {'prod': '正式环境', 'test': '测试环境', 'dev': '开发环境'}
    if env not in tmp_dict.keys():
        return
    env_str = tmp_dict[env]
    msg = "".join(msg)
    text = """
            企业微信客户当日接受消息
            环境：{}
            内容：{}
        """.format(env_str, msg)
    data = {
        "msg_type": "text",
        "content": {
            "text": text
        }
    }
    headers = {"Content-Type": "application/json"}
    requests.request("POST", settings.LARK_WORK_WECHAT_MESSAGE_ROBOT, headers=headers, data=json.dumps(data))


def push_lark_message(text, lark_robot):
    data = {
        "msg_type": "text",
        "content": {
            "text": text
        }
    }
    headers = {"Content-Type": "application/json"}
    requests.request("POST", lark_robot, headers=headers, data=json.dumps(data))


@shared_task(queue='default', ignore_result=True)
def send_lark_message(ids, message_type, project_id=None, sender_id=None, receiver_id=None):
    env = get_settings()
    env = env.split('.')[1]
    tmp_dict = {'prod': '正式环境', 'test': '测试环境', 'dev': '开发环境', 'local': '开发环境'}
    if env not in tmp_dict.keys():
        return
    env_str = tmp_dict[env]
    if message_type == 'success_pay':
        interview = models.ProjectInterview.objects.get(pk=ids[0])
        interview_source = personal_user_public.get_to_c_interview_source(interview)
        user_source, referrer_name = personal_user_public.get_to_c_user_invite_info(interview)
        if referrer_name:
            user_source = f"{user_source}-{referrer_name}"
        text = f"""C端一对一辅导提醒  {env_str}
客户：{interview.public_attr.target_user.cover_name}
教练：{interview.public_attr.user.cover_name}
辅导时间：{interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + '~' + interview.public_attr.end_time.strftime('%H:%M')}
辅导来源：{interview_source}
用户来源: {user_source}"""

        lark_robot = settings.LARK_WECHAT_PAY_ROBOT if env_str == '正式环境' else settings.DEV_TEST_LARK_ROBOT
    elif message_type == 'unconfirmed_interview':
        interview = models.ProjectInterview.objects.get(id=ids[0])
        interview_source = personal_user_public.get_to_c_interview_source(interview)
        user_source, referrer_name = personal_user_public.get_to_c_user_invite_info(interview)
        if referrer_name:
            user_source = f"{user_source}-{referrer_name}"
        text = f"""预约未确认提醒  {env_str}
教练：{interview.public_attr.user.cover_name}
客户：{interview.public_attr.target_user.cover_name}
辅导时间：{interview.public_attr.start_time.strftime('%Y-%m-%d %H:%M') + '~' + interview.public_attr.end_time.strftime('%H:%M')}
辅导来源：{interview_source}
用户来源: {user_source}"""

        lark_robot = settings.LARK_WECHAT_PAY_ROBOT if env_str == '正式环境' else settings.DEV_TEST_LARK_ROBOT
    elif message_type == 'add_personal_apply':
        personal_apply = models.PersonalApply.objects.get(pk=ids[0])
        # 见习教练转个人教练可能没有班次，通知中不能是None字符串
        text = f"""申请成为教练提醒  {env_str}
学员姓名：{personal_apply.true_name}
学员班次：{personal_apply.user_class if personal_apply.user_class else ''}
学员手机号：{personal_apply.phone}
学员邮箱：{personal_apply.email}"""

        lark_robot = settings.PERSONAL_APPLY_PROJECT_LARK_ROBOT if env_str == '正式环境' else settings.DEV_TEST_LARK_ROBOT
    elif message_type == 'project_match_coach':
        project = models.Project.objects.get(pk=ids[0])
        text = f"""企业项目匹配教练提醒  {env_str}
项目名称：{project.full_name}
需要的教练数量：{project.required_coach_count}
客户经理：{project.account_manager['true_name'] if project.account_manager else ''}
截止时间：{project.providing_coach_list_time.strftime('%Y-%m-%d')}"""

        lark_robot = settings.PERSONAL_APPLY_PROJECT_LARK_ROBOT if env_str == '正式环境' else settings.DEV_TEST_LARK_ROBOT
    elif message_type == 'coach_offer_add':
        coach_offer = models.CoachOffer.objects.get(id=ids[0])
        text = f"""同意加入项目提醒  {env_str}
教练姓名： {coach_offer.coach.user.cover_name}
项目名称：{coach_offer.project_offer.project.full_name}
最多服务客户数量：{coach_offer.max_customer_count if coach_offer.max_customer_count else '不限制'}"""

        lark_robot = settings.PERSONAL_APPLY_PROJECT_LARK_ROBOT if env_str == '正式环境' else settings.DEV_TEST_LARK_ROBOT
    elif message_type == 'coach_offer_rejected':
        coach_offer = models.CoachOffer.objects.get(id=ids[0])
        text = f"""拒绝加入项目提醒  {env_str}
教练姓名： {coach_offer.coach.user.cover_name}
项目名称：{coach_offer.project_offer.project.full_name}
拒绝原因：{coach_offer.refuse_reason}"""

        lark_robot = settings.PERSONAL_APPLY_PROJECT_LARK_ROBOT if env_str == '正式环境' else settings.DEV_TEST_LARK_ROBOT
    elif message_type == 'regular_add_personal_apply':
        personal_apply = models.PersonalApply.objects.filter(id__in=ids)
        text = f"""见习教练提交简历申请通知  {env_str}
申请人数：{personal_apply.count()}
学员姓名：{','.join([item.true_name for item in personal_apply.all()])}"""

        lark_robot = settings.PERSONAL_APPLY_PROJECT_LARK_ROBOT if env_str == '正式环境' else settings.DEV_TEST_LARK_ROBOT
    elif message_type == 'delete_settlement_orders':
        text = f"""结算单删除提醒  {env_str}
结算单编号：{ids}"""
        lark_robot = settings.PERSONAL_APPLY_PROJECT_LARK_ROBOT if env_str == '正式环境' else settings.DEV_TEST_LARK_ROBOT
    else:
        return
    # 根据关键字判断是否为测试数据
    keywords = ['彭佳丽测试', '#测试项目#', '#测试数据#', 'testuser__']
    test_robot = False
    if any(keyword in text for keyword in keywords):
        lark_robot = settings.DEV_TEST_LARK_ROBOT
        test_robot = True
    push_lark_message(text, lark_robot)

    # 记录通知信息
    if not test_robot:
        PlatformNotification.create_notification(
            subject=text.split('\n')[0],
            template_name=message_type,
            channel=NoticeChannelTypeEnum.feishu.value,
            project_id=project_id,
            receiver_contact=lark_robot,
            status=1,
            sender_id=sender_id,
            receiver_id=receiver_id
        )
