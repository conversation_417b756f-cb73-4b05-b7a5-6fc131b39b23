import re

from datetime import datetime
from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from django.db import transaction
from django.db.models import Q

from utils.messagecenter import center
from ..common import company_public
from ..enum.project_member_enum import ProjectMemberRoleEnum
from ..models import ProjectInterview, Project, UserAnswer, Company, User, CompanyMember, CompanyManage, UserBackend, \
    Role, ProjectMember
from .company_actions import CompanySerializers, ProjectCompanyListSerializer
from rest_framework import mixins
from rest_framework import viewsets


from utils.pagination import StandardResultsSetPagination
from utils.api_response import success_response, parameter_error_response
from utils import aesencrypt, randomPassword
from wisdom_v2.enum.user_backend_enum import UserBackendTypeEnum


class CompanyViewSet(viewsets.ModelViewSet):
    queryset = Company.objects.filter(deleted=False).order_by('-created_at')
    serializer_class = CompanySerializers

    @swagger_auto_schema(
        operation_id='公司列表',
        operation_summary='公司列表',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字（企业名称和简称）', type=openapi.TYPE_NUMBER),
            openapi.Parameter('short', openapi.IN_QUERY, description='企业简称', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['公司相关']
    )
    def list(self, request, *args, **kwargs):
        # 只有项目管理员可以调用查看，根据请求的登录用户信息判断是否为管理员，返回所有企业
        # user = request.user
        # print(user)
        # if user.user_type == 1:
        #     return parameter_error_response('用户无权查看企业信息')

        company = self.get_queryset()
        if request.query_params.get('keyword', None):
            company = company.filter(Q(name__icontains=request.query_params.get('keyword')) |
                                     Q(short__icontains=request.query_params.get('keyword'))).order_by('-created_at')
        if request.query_params.get('short'):
            company = company.filter(short__icontains=request.query_params.get('short')).order_by('-created_at')

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(company, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)

    @swagger_auto_schema(
        operation_id='公司详情',
        operation_summary='公司详情',
        manual_parameters=[
            openapi.Parameter('company_id', openapi.IN_QUERY, description='公司id', type=openapi.TYPE_NUMBER),
        ],
        tags=['公司相关']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def company_detail(self, request, *args, **kwargs):
        try:
            instance = Company.objects.get(pk=request.query_params.get('company_id', 0))
        except Company.DoesNotExist as e:
            return parameter_error_response()

        serializer = self.get_serializer(instance)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='修改公司信息',
        operation_summary='修改公司信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'company_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='公司ID'),
                'short': openapi.Schema(type=openapi.TYPE_STRING, description='公司简称'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='公司名称'),
                'logo': openapi.Schema(type=openapi.TYPE_STRING, description='公司logo'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='公司简介'),
                'company_attr': openapi.Schema(type=openapi.TYPE_NUMBER, description='公司属性'),
                'industry': openapi.Schema(type=openapi.TYPE_NUMBER, description='公司行业'),
                'scale': openapi.Schema(type=openapi.TYPE_NUMBER, description='公司规模'),
                'video': openapi.Schema(type=openapi.TYPE_STRING, description='视频介绍'),
                'website': openapi.Schema(type=openapi.TYPE_STRING, description='公司官网'),
                'deleted': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='设置删除'),
                'logo_remark': openapi.Schema(type=openapi.TYPE_STRING, description='logo备注'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='企业邮箱后缀 @xxx.com')

            }
        ),
        tags=['公司相关']
    )
    @action(methods=['post'], detail=False, url_path='update')
    def company_update(self, request, *args, **kwargs):
        try:
            instance = Company.objects.get(pk=request.data.get('company_id', 0))
        except Company.DoesNotExist as e:
            return parameter_error_response()
        data = request.data.copy()
        if 'email' in data.keys() and data['email']:
            email = data['email']
            rule = r'^@[A-Za-z0-9-]+(\.[A-Z|a-z]{2,})+'
            if not re.match(rule, email):
                return parameter_error_response('企业邮箱后缀格式错误')

        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        company_public.update_company_tag(instance.id)
        return success_response(serializer.data)

    @swagger_auto_schema(
        operation_id='创建公司信息',
        operation_summary='创建公司信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'short': openapi.Schema(type=openapi.TYPE_STRING, description='公司简称'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='公司名称'),
                'logo': openapi.Schema(type=openapi.TYPE_STRING, description='公司logo'),
                'brief': openapi.Schema(type=openapi.TYPE_STRING, description='公司简介'),
                'scale': openapi.Schema(type=openapi.TYPE_STRING, description='公司规模'),
                'video': openapi.Schema(type=openapi.TYPE_STRING, description='视频介绍'),
                'website': openapi.Schema(type=openapi.TYPE_STRING, description='公司官网'),
                'company_attr': openapi.Schema(type=openapi.TYPE_STRING, description='公司属性'),
                'industry': openapi.Schema(type=openapi.TYPE_STRING, description='公司行业'),
                'logo_remark': openapi.Schema(type=openapi.TYPE_STRING, description='logo备注'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='企业邮箱后缀 @xxx.com')
            }
        ),
        tags=['公司相关']
    )
    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        if 'email' in data.keys() and data['email']:
            email = data['email']
            rule = r'^@[A-Za-z0-9-]+(\.[A-Z|a-z]{2,})+'
            if not re.match(rule, email):
                return parameter_error_response('企业邮箱后缀格式错误')
        return success_response(super(CompanyViewSet, self).create(request, *args, **kwargs).data)

    @swagger_auto_schema(
        operation_id='创建企业管理员',
        operation_summary='创建企业管理员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='姓名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'company_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业id'),
                'project_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='项目id')
            }
        ),
        tags=['公司相关']
    )
    @action(methods=['post'], detail=False, url_path='add_company_manage')
    def add_company_manage(self, request, *args, **kwargs):
        try:
            data = request.data
            name = data['name']
            company_id = data.get('company_id')
            true_name = data['true_name']
            email = data['email']
            project_id = data.get('project_id')
            if not company_id and project_id:
                project = Project.objects.get(pk=project_id)
                company_id = project.company.id
            company = Company.objects.get(id=company_id)
        except:
            return parameter_error_response()
        # 如果企业员工存在，则直接配置为管理员
        user = None
        if User.objects.filter(name=name, deleted=False).exists():
            user = User.objects.filter(name=name, deleted=False).first()
        if User.objects.filter(email=email, deleted=False).exists():
            user = User.objects.filter(email=email, deleted=False).first()
        try:
            with transaction.atomic():
                if not user:
                    pwd = randomPassword()
                    user = User.objects.create(true_name=true_name, email=email, password=aesencrypt(pwd),
                                            name=name)
                # 企业管理员同时是企业员工
                CompanyMember.objects.get_or_create(company=company, user=user, deleted=False)

                role = Role.objects.filter(name='企业管理员', deleted=False).first()
                UserBackend.objects.get_or_create(
                    user=user, company=company, project_id=project_id,
                    role_id=role.id,
                    deleted=False
                )
                return success_response()
        except Exception as e:
            return parameter_error_response(str(e))

    @swagger_auto_schema(
        operation_id='编辑企业管理员',
        operation_summary='编辑企业管理员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业用户id'),
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='用户名'),
                'true_name': openapi.Schema(type=openapi.TYPE_STRING, description='姓名'),
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='邮箱'),
                'company_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业id')

            }
        ),
        tags=['公司相关']
    )
    @action(methods=['put'], detail=False, url_path='edit_company_manage')
    def edit_company_manage(self, request, *args, **kwargs):
        try:
            data = request.data
            id = data['id']
            company_id = data['company_id']
            name = data['name']
            true_name = data['true_name']
            email = data['email']
            user_backend = UserBackend.objects.get(pk=id, deleted=False)
        except:
            return parameter_error_response()

        if User.objects.filter(name=name, deleted=False).exists() and name != user_backend.user.name:
            return parameter_error_response('用户名已存在')
        if User.objects.filter(email=email, deleted=False).exists() and email != user_backend.user.email:
            return parameter_error_response('用户邮箱已存在')

        try:
            with transaction.atomic():
                user = user_backend.user
                user.name = name
                user.true_name = true_name
                user.email = email
                user.save()
                return success_response()
        except:
            return parameter_error_response()

    @swagger_auto_schema(
        operation_id='删除企业管理员',
        operation_summary='删除企业管理员',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业管理员id'),
                'company_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='企业id')
            }
        ),
        tags=['公司相关']
    )
    @action(methods=['post'], detail=False, url_path='del_company_manage')
    def del_company_manage(self, request, *args, **kwargs):
        try:
            data = request.data
            id = data['id']
            company_id = data['company_id']

            user_backend = UserBackend.objects.get(pk=id, deleted=False)
        except:
            return parameter_error_response()
        try:
            with transaction.atomic():
                # 一个企业管理员可能绑定不同项目,要清除这个用户下所有的
                user = user_backend.user
                UserBackend.objects.filter(user_id=user.id).update(deleted=True)
                return success_response()
        except:
            return parameter_error_response()

    @swagger_auto_schema(
        operation_id='添加项目中关联公司列表',
        operation_summary='添加项目中关联公司列表',
        manual_parameters=[
            openapi.Parameter('keyword', openapi.IN_QUERY, description='关键字（企业名称和简称）', type=openapi.TYPE_STRING),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['公司相关']
    )
    @action(methods=['get'], detail=False, url_path='project_company_list', serializer_class=ProjectCompanyListSerializer)
    def project_company_list(self, request, *args, **kwargs, ):
        company = self.get_queryset()
        if request.query_params.get('keyword', None):
            company = company.filter(Q(name__icontains=request.query_params.get('keyword')) |
                                     Q(short__icontains=request.query_params.get('keyword'))).order_by('-created_at')
        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(company, self.request)
        serializer = self.get_serializer(page_list, many=True)
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response)


