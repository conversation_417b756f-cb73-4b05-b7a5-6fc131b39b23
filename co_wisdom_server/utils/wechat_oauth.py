from __future__ import unicode_literals

import logging
import json
from celery import shared_task
import redis
import base64
import requests

from Crypto.Cipher import AES

from django.conf import settings

from utils.aliyun_sls_log import AliyunSlsLogLayout
from utils.feishu_robot import push_wx_error_message
from utils.message.error_message import send_backend_error_message

access_token_redis = redis.Redis.from_url(settings.WECHAT_ACCESS_TOKEN_URL)

error_logging = logging.getLogger('api_action')
wechat_http = requests.Session()


# 微信access_token过期更新
def wechat_hook(response, *args, **kwargs):

    if response.ok:
        try:
            data = response.request.body
            body = json.loads(data) if data else {}
            if response.json().get('errcode') in [40001] and not body.get('check'):
                # 删除缓存
                access_token_redis.delete('access_token')

                # 替换url中的access_token参数值
                url = response.request.url
                api, url = url.split('?')
                params = []
                for item in url.split('&'):
                    if 'access_token' in item:
                        item = 'access_token={}'.format(WeChatMiniProgram().get_access_token())
                    params.append(item)
                url = api + '?' + '&'.join(params)

                # 次数标记，防止循环调用。
                if body:
                    body['check'] = 'true'
                else:
                    body = {'check': 'true'}

                # 记录access_token过期后重新发起的请求
                AliyunSlsLogLayout().send_third_api_log(
                    content={'msg': {'name': api, 'url': url, 'params': body}},
                    message='微信access_token重置',
                )

                # 重新请求
                if response.request.method == 'POST':
                    return wechat_http.post(url, json=body)
                elif response.request.method == 'GET':
                    return wechat_http.get(url, json=body)
                else:
                    return response
        except Exception as e:
            msg = {
                'error': str(e),
                'request': response.request.body,
                'response': response.text,
                'url': response.request.url,
            }
            push_wx_error_message(name='微信access_token重置错误', level='error', content={
                'task_name': 'wechat_hook',
                'msg': f'微信刷新access_token代码错误：{msg}'})
            return response


wechat_http.hooks["response"] = [wechat_hook]


class WXBizDataCrypt:
    def __init__(self, appId, sessionKey):
        self.appId = appId
        self.sessionKey = sessionKey

    def decrypt(self, encryptedData, iv):
        # base64 decode
        sessionKey = base64.b64decode(self.sessionKey)
        encryptedData = base64.b64decode(encryptedData)
        iv = base64.b64decode(iv)

        cipher = AES.new(sessionKey, AES.MODE_CBC, iv)
        res = self._unpad(cipher.decrypt(encryptedData)).decode('utf-8')
        decrypted = json.loads(res)

        if decrypted['watermark']['appid'] != self.appId:
            raise Exception('Invalid Buffer')

        return decrypted

    def _unpad(self, s):
        return s[:-ord(s[len(s) - 1:])]


class WeChatError(ValueError):
    pass


class WeChatOauth(object):
    """ 微信授权 """

    def __init__(self, app_id=None, app_secret=None):
        self.requests = requests.Session()

        self.app_id = settings.APP_ID if not app_id else app_id
        self.app_secret = settings.APP_SECRET if not app_secret else app_secret
        self.oauth_url = "https://open.weixin.qq.com/connect/oauth2/authorize"
        self.access_token_url = "https://api.weixin.qq.com/sns/oauth2/access_token"
        self.refresh_token_url = "https://api.weixin.qq.com/sns/oauth2/refresh_token"
        self.userinfo_url = "https://api.weixin.qq.com/sns/userinfo"
        self.jscode2session_url = "https://api.weixin.qq.com/sns/jscode2session"
        self.auth_url = "https://api.weixin.qq.com/sns/auth"

    def get(self, url, params):
        """ 封装get方法
        """
        response = wechat_http.get(url, params=params)
        data = json.loads(response.content.decode("utf-8"))
        if data.get('errcode'):
            msg = "%(errcode)d %(errmsg)s" % data
            return {'err': msg}
        return data

    def post(self, url, data):
        response = wechat_http.post(url, json=data)
        data = json.loads(response.content.decode("utf-8"))
        if data.get('errcode'):
            msg = "%(errcode)d %(errmsg)s" % data
            return {'err': msg}
        return data

    def get_phone_number(self, phone_code):
        url = 'https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s' % \
              WeChatMiniProgram().get_access_token()
        data = {"code": phone_code}
        data = self.post(url, data)
        return data

    def get_userinfo(self, openid):
        url = "https://api.weixin.qq.com/sns/userinfo"
        params = {
            'access_token':  WeChatMiniProgram().get_access_token(),
            'openid': openid
        }

        data = self.get(url, params)
        return data

    def access_token(self, code):
        """
        获取令牌 access_token
        """
        args = dict()
        args.setdefault("appid", self.app_id)
        args.setdefault("secret", self.app_secret)
        args.setdefault("code", code)
        args.setdefault("grant_type", "authorization_code")

        return self.get(self.access_token_url, args)

    def jscode2session(self, js_code):
        """
        小程序获取 session_key 和 openid
        :param js_code: 使用wx.login登录时获取到的code参数数据
        """

        args = dict()
        args.setdefault("appid", self.app_id)
        args.setdefault("secret", self.app_secret)
        args.setdefault("js_code", js_code)
        args.setdefault("grant_type", "authorization_code")

        response = self.get(self.jscode2session_url, args)

        if response.get('err'):
            send_backend_error_message(
                '小程序获取session_key和openid失败',
                f'func_name:jscode2session; args: {args}')
        return response


class WeChatMiniProgram(object):

    def __init__(self):
        self.app_id = settings.APP_ID
        self.app_secret = settings.APP_SECRET
        self.api_url = 'https://api.weixin.qq.com'

        self.env_site_url = {
            'https://www.qzcoach.com/': 'release',
            'https://www.qzcoachtest.com/': 'trial',
            'https://www.qzcoach.cn/': 'develop',
        }

    def get_access_token(self):
        """ 获取access_token"""
        url = 'https://api.weixin.qq.com/cgi-bin/stable_token'
        args = dict()
        args.setdefault('grant_type', 'client_credential')
        args.setdefault('appid', self.app_id)
        args.setdefault('secret', self.app_secret)

        if not access_token_redis.get('access_token'):
            resp = requests.get(url, params=args)
            data = json.loads(resp.content.decode("utf-8"))
            if data.get('errcode'):
                msg = "%(errcode)d %(errmsg)s" % data
                return WeChatError(msg)
            access_token_redis.set('access_token', data['access_token'], ex=data['expires_in'])
            return data['access_token']
        else:
            return access_token_redis.get('access_token').decode('utf8')


    @shared_task(queue='task', ignore_result=True, soft_time_limit=15)
    def template_send(template_id, touser, value_list, url=None, project_id=None, receiver_id=None, template_name=None, **kwargs):
        """
        发送模板消息
        :params template_id: 模板id
        :params touser: openid
        :params value_list: value_list 消息的列表
        :params data: 模板消息对应的内容
        :params url: 跳转地址
        :params project_id: 项目ID
        :params receiver_id: 接收者ID
        :params template_name: 模板名称
        """
        from wisdom_v2.models_file.platform_notification import PlatformNotification
        from wisdom_v2.enum.service_content_enum import NoticeChannelTypeEnum

        if not touser:
            return
        
        wechat = WeChatMiniProgram()

        kwargs.setdefault("template_id", template_id)
        kwargs.setdefault("touser", touser)
        kwargs.setdefault("data", value_list)
        url and kwargs.setdefault("page", url)
        kwargs.setdefault("miniprogram_state", wechat.env_site_url.get(settings.SITE_URL))
        
        if not isinstance(touser, str):
            touser = touser.openid

        # 准备通知记录数据
        # first object in value_list is subject
        subject = next(iter(value_list.values()))['value']
        receiver_contact = touser
        status = 1  # 默认成功
        error_message = None

        try:
            api_url = f'{wechat.api_url}/cgi-bin/message/subscribe/send?access_token={wechat.get_access_token()}'
            rsp = wechat_http.post(api_url, json=kwargs)
            if rsp.ok:
                data = rsp.json()
                # 0：发送成功 43101：未订阅或一次性订阅次数已使用
                if data.get('errcode') not in [43101, 0]:
                    status = 2  # 失败
                    error_message = f"errcode: {data.get('errcode')}, errmsg: {data.get('errmsg')}"
                    send_backend_error_message(
                        '微信模版消息发送失败',
                        f'func_name:template_send; template_id: {template_id}; touser: {touser}; error: {data}; data: {kwargs}')

                # 记录通知到platform_notification
                PlatformNotification.create_notification(
                    subject=subject,
                    template_name=template_name,
                    channel=NoticeChannelTypeEnum.mini_program.value,
                    project_id=project_id,
                    receiver_id=receiver_id,
                    receiver_contact=receiver_contact,
                    status=status,
                    error_message=error_message,
                    extra_data={
                        'value_list': value_list,
                        'url': url,
                        'response': data
                    }
                )

                return data
            else:
                status = 2  # 失败
                error_message = f"HTTP请求失败: {rsp.status_code}"
                send_backend_error_message(
                    '微信模版消息发送失败',
                    f'func_name:template_send; template_id: {template_id}; touser: {touser}; error: {rsp}; data: {kwargs}')

                # 记录失败通知
                PlatformNotification.create_notification(
                    subject=subject,
                    template_name=template_name,
                    channel=NoticeChannelTypeEnum.mini_program.value,
                    project_id=project_id,
                    receiver_id=receiver_id,
                    receiver_contact=receiver_contact,
                    status=status,
                    error_message=error_message,
                    extra_data={
                        'value_list': value_list,
                        'url': url
                    }
                )

                return None
        except WeChatError as e:
            status = 2  # 失败
            error_message = f"WeChatError: {str(e)}"
            send_backend_error_message(
                '微信模版消息发送失败',
                f'func_name:template_send; template_id: {template_id}; touser: {touser}; error: {str(e)}; data: {kwargs}')

            # 记录失败通知
            PlatformNotification.create_notification(
                subject=subject,
                template_name=template_name,
                channel=NoticeChannelTypeEnum.mini_program.value,
                project_id=project_id,
                receiver_id=receiver_id,
                receiver_contact=receiver_contact,
                status=status,
                error_message=error_message,
                extra_data={
                    'value_list': value_list,
                    'url': url
                }
            )

            return None

    def get_url_scheme(self, path, query):
        url = f'{self.api_url}/wxa/generatescheme?access_token={self.get_access_token()}'

        data = {
            'jump_wxa': {
                'path': path,
                'query': query,
                'env_version': self.env_site_url.get(settings.SITE_URL)
            },
            'expire_type': 1,
            'expire_interval': 30
        }
        # {"errcode": 0,"errmsg": "ok","openlink": Scheme, }
        raw = wechat_http.post(url=url, json=data)

        if raw.ok:
            if raw.json().get('errcode') == 0:
                return True, raw.json().get('openlink')
            else:
                return False, raw.json().get('errmsg')
        return False, raw.status_code

    def get_url_link(self, path, query):
        env_version = self.env_site_url.get(settings.SITE_URL)
        # 生成缓存key，包含path、query和env_version作为唯一标识
        cache_key = f'url_link:{env_version}:{path}:{query}'

        # 先尝试从Redis获取缓存
        cached_link = access_token_redis.get(cache_key)
        if cached_link:
            return True, cached_link.decode('utf8')

        url = f'{self.api_url}/wxa/generate_urllink?access_token={self.get_access_token()}'

        data = {
            'path': path,
            'query': query,
            'env_version': env_version
        }

        # {"errcode": 0,"errmsg": "ok","url_link": Scheme, }
        raw = wechat_http.post(url=url, json=data)

        try:
            if raw.ok:
                if raw.json().get('errcode') == 0:
                    url_link = raw.json().get('url_link')
                    # 将生成的链接存入Redis，不设置过期时间(长期有效)
                    access_token_redis.set(cache_key, url_link)
                    return True, url_link
                else:
                    send_backend_error_message(
                        '获取小程序短链接失败', f'get_url_link; data: {data}; raw: {raw.json()}')
                    return False, None
            send_backend_error_message(
                '获取小程序短链接失败', f'get_url_link; data: {data}; raw: {raw.content}')
            return False, None
        except Exception as e:
            send_backend_error_message(
                '获取小程序短链接失败', f'get_url_link; data: {data}; error: {str(e)}; raw: {raw.content}')
            return False, None

    def get_miniapp_qrcode(self, scene, page='pages/user/myResume'):
        url = f'{self.api_url}/wxa/getwxacodeunlimit?access_token={self.get_access_token()}'
        data = {
            'page': page,
            'scene': scene,
            'env_version': self.env_site_url.get(settings.SITE_URL),
            'check_path': False
        }
        raw = wechat_http.post(url=url, json=data)
        try:
            if raw.ok:
                try:
                    json_content = raw.json()
                    if json_content and json_content.get('errcode'):
                        send_backend_error_message(
                            '获取小程序二维码失败', f'get_miniapp_qrcode; data: {data}; raw: {json_content}')
                        return False, None
                except ValueError:
                    # 如果响应内容不是 JSON，那么我们就假定它是我们期待的二进制数据
                    return True, raw.content
            send_backend_error_message(
                '获取小程序二维码失败', f'get_miniapp_qrcode; data: {data}; raw: {raw.content}')
            return False, None
        except Exception as e:
            send_backend_error_message(
                '获取小程序二维码失败', f'get_miniapp_qrcode; data: {data}; error: {str(e)}; raw: {raw.content}')
            return False, None


def retry_template_send(url, params, data):
    """ access_token失效 重试模板消息发送 """
    access_token_redis.delete('access_token')
    access_token = WeChatMiniProgram().get_access_token()
    params.setdefault("access_token", access_token)
    headers = {}
    data = json.dumps(data, ensure_ascii=False).encode()
    headers["Content-Type"] = "application/json;charset=UTF-8"
    res = requests.post(url=url, params=params, data=data, headers=headers)
    response = json.loads(res.content.decode())
    return response


class AppWechatOauth:
    """
    App 微信授权
    """

    def __init__(self):
        self.requests = requests.Session()
        self.app_id = settings.OPEN_APP_ID
        self.app_secret = settings.OPEN_APP_SECRET
        self.oauth_url = "https://open.weixin.qq.com/connect/oauth2/authorize"
        self.access_token_url = "https://api.weixin.qq.com/sns/oauth2/access_token"
        self.refresh_token_url = "https://api.weixin.qq.com/sns/oauth2/refresh_token"
        self.userinfo_url = "https://api.weixin.qq.com/sns/userinfo"
        self.auth_url = "https://api.weixin.qq.com/sns/auth"

    def get(self, url, params):
        """ 封装get方法
        """
        response = self.requests.get(url, params=params)
        data = json.loads(response.content.decode("utf-8"))
        if data.get('errcode'):
            msg = "%(errcode)d %(errmsg)s" % data
            return {'err': msg}
        return data

    def get_access_token(self, code):
        args = dict()
        args.setdefault('grant_type', 'authorization_code')
        args.setdefault('appid', self.app_id)
        args.setdefault('secret', self.app_secret)
        args.setdefault('code', code)

        return self.get(self.access_token_url, args)

    def get_userinfo(self, access_token, openid):
        args = dict()
        args.setdefault('access_token', access_token)
        args.setdefault('openid', openid)

        return self.get(self.userinfo_url, args)
