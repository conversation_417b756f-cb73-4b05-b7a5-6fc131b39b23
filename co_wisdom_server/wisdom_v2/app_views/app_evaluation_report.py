import json
from decimal import Decimal, ROUND_HALF_UP

from drf_yasg import openapi
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from utils import task
from utils.api_response import success_response, parameter_error_response
from utils.miniapp_version_judge import compare_version
from utils.queryset import multiple_field_distinct
from .app_project_docs_action import AppProjectDocsListListSerializer
from ..enum.project_enum import ProjectDocsTypeEnum
from ..enum.project_interview_enum import DataType
from ..enum.user_backend_enum import UserBackendTypeEnum
from ..views.constant import MANAGE_EVALUATION, LBI_EVALUATION
from .app_evaluation import EvaluationSerializers
from ..models import EvaluationReport, User, EvaluationReportScore, Article, GrowthGoals, UserBackend, ProjectDocs
from rest_framework import serializers, viewsets
from wisdom_v2.app_views.app_coachee_growth_actions import AppCoacheeGrowthGoalsViewSerializers

from utils.pagination import StandardResultsSetPagination
from wisdom_v2.enum.service_content_enum import EvaluationReportTypeEnum, PdfReportTypeEnum

from wisdom_v2.views import constant


class EvaluationReportScoreSerializers(serializers.ModelSerializer):
    section = serializers.SerializerMethodField()
    growth_target = serializers.SerializerMethodField()
    comment = serializers.SerializerMethodField()

    class Meta:
        model = EvaluationReportScore
        exclude = ['updated_at', 'created_at']

    def get_section(self, obj):
        return obj.get_section_display()

    def get_growth_target(self, obj):
        obj_lst = GrowthGoals.objects.filter(
            growth_evaluation_report_score_related__evaluation_report_score_id=obj.pk, deleted=False)
        if obj_lst.exists():
            return AppCoacheeGrowthGoalsViewSerializers(obj_lst, many=True).data
        return []

    def get_comment(self, obj):
        comment_list = [
            '了解人性，具有同理心，能建立起人与人之间的信任',  # 理解人
            '设定清晰的目标，并能够将目标达成共识',  # 共识目标
            '支持下属在解决问题，完成任务过程中获得成长',  # 启发人
            '把想法变成落地可行的行动与任务',  # 坚定行动
            '在解决问题、完成任务的过程中促进个人的成长',  # 发展人
        ]
        if len(comment_list) > obj.section:
            return comment_list[obj.section]


class EvaluationReportDetailSerializers(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(format='%Y-%m-%d')
    evaluation = serializers.SerializerMethodField()
    sub_score = serializers.SerializerMethodField()
    username = serializers.SerializerMethodField()
    article = serializers.SerializerMethodField()
    comparative_text = serializers.SerializerMethodField()
    evaluation_report_type = serializers.SerializerMethodField()


    class Meta:
        model = EvaluationReport
        exclude = ['updated_at', 'public_attr']

    def get_evaluation_report_type(self, obj):
        evaluation_report_config = obj.evaluation.evaluation_report_config.filter(deleted=False).first()
        return evaluation_report_config.type if evaluation_report_config else None

    def get_comparative_text(self, obj):
        # 查看是否第一个完成
        reports = EvaluationReport.objects.filter(
            public_attr__project=obj.public_attr.project, deleted=False,
            evaluation__code=MANAGE_EVALUATION).order_by('created_at')
        if reports.count() == 1 and reports.first().pk == obj.pk:
            return '正在和0个同事PK，我的成绩在本项目中展示排名第1'

        min_reports_count = reports.filter(score__lte=obj.score).exclude(pk=obj.pk).count()
        if min_reports_count == 0:
            percentage = '10%'
        else:
            # 使用Decimal确保精确计算，先将数值转换为字符串避免初始的浮点数不精确
            min_reports_decimal = Decimal(str(min_reports_count))
            total_reports_decimal = Decimal(str(reports.count()))
            # 计算百分比，乘以100，并且四舍五入到一位小数
            percentage = (min_reports_decimal / total_reports_decimal * Decimal('100')).quantize(
                Decimal('0.0'), rounding=ROUND_HALF_UP)
            percentage = f'{percentage}%'
        return f'正在和{reports.count()-1}个同事PK，我的成绩超过了他们中的{percentage}'

    def get_evaluation(self, obj):
        return EvaluationSerializers(obj.evaluation, many=False).data

    def get_sub_score(self, obj):
        sub_score_list = EvaluationReportScore.objects.filter(report=obj, deleted=False)
        lst = []
        if sub_score_list:
            data1 = EvaluationReportScoreSerializers(sub_score_list.filter(section=2).first()).data
            lst.append(data1)
            data2 = EvaluationReportScoreSerializers(sub_score_list.filter(section=0).first()).data
            lst.append(data2)
            data3 = EvaluationReportScoreSerializers(sub_score_list.filter(section=4).first()).data
            lst.append(data3)
            data4 = EvaluationReportScoreSerializers(sub_score_list.filter(section=1).first()).data
            lst.append(data4)
            data5 = EvaluationReportScoreSerializers(sub_score_list.filter(section=3).first()).data
            lst.append(data5)
            return lst

    def get_username(self, obj):
        return obj.public_attr.user.cover_name

    def get_article(self, obj):
        power_tag = obj.power_tag
        result = []
        if power_tag:
            for tag in power_tag:
                if tag == '建立信任':
                    article = Article.objects.filter(title__icontains='培养明智的同理心').values('id', 'title').first()
                    if article:
                        result.append(article)
                elif tag == '积极聆听':
                    article = Article.objects.filter(title__icontains='谈话中提升倾听能力的六个练习').values('id', 'title').first()
                    if article:
                        result.append(article)
                elif tag == '启发提问':
                    article = Article.objects.filter(title__icontains='能够引发思考的问题清单').values('id', 'title').first()
                    if article:
                        result.append(article)
                elif tag == '共识目标':
                    article = Article.objects.filter(title__icontains='五个问题助你锚定谈话目标').values('id', 'title').first()
                    if article:
                        result.append(article)
                elif tag == '辅导反馈':
                    article = Article.objects.filter(title__icontains='及时反馈助力员工成长').values('id', 'title').first()
                    if article:
                        result.append(article)
                elif tag == '促进行动':
                    article = Article.objects.filter(title__icontains='如何让员工的行动更有力量').values('id', 'title').first()
                    if article:
                        result.append(article)
        return result


class EvaluationReportSerializers(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(format='%Y-%m-%d %H:%M')
    evaluation_code = serializers.CharField(source='evaluation.code', help_text='测评编号')
    title = serializers.CharField(source='evaluation.name', help_text='测评名称')
    evaluation = serializers.SerializerMethodField()
    data_type = serializers.SerializerMethodField(help_text='数据类型')
    evaluation_report_type = serializers.SerializerMethodField(help_text='测评报告类型')

    class Meta:
        model = EvaluationReport
        fields = ['created_at', 'evaluation', 'id', 'evaluation_code', 'data_type', 'title', 'evaluation_report_type']

    def get_data_type(self, obj):
        return DataType.evaluation.value

    def get_evaluation(self, obj):
        return EvaluationSerializers(obj.evaluation, many=False).data

    def get_evaluation_report_type(self, obj):
        evaluation_report_config = obj.evaluation.evaluation_report_config.filter(deleted=False).first()
        return evaluation_report_config.type if evaluation_report_config else None


class EvaluationReportViewSet(viewsets.ModelViewSet):
    queryset = EvaluationReport.objects.filter(deleted=False)
    serializer_class = EvaluationReportSerializers

    @swagger_auto_schema(
        operation_id='测评报告列表',
        operation_summary='测评报告列表',
        manual_parameters=[
            openapi.Parameter('user_id', openapi.IN_QUERY, description='用户id', type=openapi.TYPE_NUMBER,
                              required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description='第几页', type=openapi.TYPE_NUMBER),
            openapi.Parameter('page_size', openapi.IN_QUERY, description='每页显示条数默认15条', type=openapi.TYPE_NUMBER)
        ],
        tags=['app测评']
    )
    def list(self, request):
        try:
            user_id = int(request.query_params.get('user_id', 0))
            user = User.objects.get(pk=user_id)
            mp = json.loads(request.headers.get('mp')) if request.headers.get('mp') else None
        except User.DoesNotExist:
            return parameter_error_response('参数错误')

        evaluation_queryset = self.get_queryset()
        # 只有报告用户自己能看到教练型管理者测评报告
        self_only_code_list = [MANAGE_EVALUATION]
        if request.user.pk != user_id:
            evaluation_queryset = evaluation_queryset.exclude(evaluation__code__in=self_only_code_list)
        evaluation_queryset = evaluation_queryset.filter(public_attr__user=user).order_by('-updated_at')
        evaluation_serializer = self.serializer_class(evaluation_queryset, many=True).data
        data_list = evaluation_serializer

        if mp and compare_version(mp.get('version'), '2.18') >= 0:
            project_docs = ProjectDocs.objects.filter(
                project_member__user_id=user_id, file__deleted=False,
                project_docs_type=ProjectDocsTypeEnum.report, deleted=False).order_by('-file__created_at')
            project_docs = multiple_field_distinct(project_docs, ['project_member', 'file_id'])
            project_docs_data = AppProjectDocsListListSerializer(project_docs, many=True).data
            data_list += project_docs_data

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(data_list, self.request)
        response = paginator.get_paginated_response(page_list)
        return success_response(response, request=request)

    @swagger_auto_schema(
        operation_id='测评报告详情',
        operation_summary='测评报告详情',
        manual_parameters=[
            openapi.Parameter('evaluation_report_id', openapi.IN_QUERY, description='测评报告id',
                              type=openapi.TYPE_NUMBER, required=True)
        ],
        tags=['app测评']
    )
    @action(methods=['get'], detail=False, url_path='detail')
    def evaluation_report_detail(self, request, *args, **kwargs):
        try:
            instance = EvaluationReport.objects.get(pk=request.query_params.get('evaluation_report_id', 0))
        except EvaluationReport.DoesNotExist:
            return parameter_error_response('测评报告id错误')

        if instance.evaluation.code == MANAGE_EVALUATION:
            serializer = EvaluationReportDetailSerializers(instance).data
        elif instance.evaluation.code == LBI_EVALUATION:
            evaluation_report_config = instance.evaluation.evaluation_report_config.filter(deleted=False).first()
            serializer = instance.lbi_analysis
            serializer['report_name'] = instance.evaluation.name
            serializer['evaluation_report_type'] = evaluation_report_config.type if evaluation_report_config else None
            serializer['project_name'] = instance.public_attr.project.name
            serializer['coachee_info'] = {
                'name': instance.public_attr.user.cover_name
            }
            user = request.user
            if UserBackend.objects.filter(user=user, role_id=constant.PROJECT_COMPANY_ADMIN, deleted=False).exists():
                serializer['pdf_url'] = instance.company_manager_pdf_url if instance.company_manager_pdf_url \
                    else instance.pdf_url
            else:
                serializer['pdf_url'] = instance.pdf_url
        else:
            return parameter_error_response('测评报告code错误')
        return success_response(serializer, request=request)

    @swagger_auto_schema(
        operation_id='教练查看客户测评报告列表',
        operation_summary='教练查看客户测评报告列表',
        manual_parameters=[
            openapi.Parameter('coach_user_id', openapi.IN_QUERY, description='教练id',
                              type=openapi.TYPE_NUMBER, required=True),
            openapi.Parameter('coachee_user_id', openapi.IN_QUERY, description='客户id',
                              type=openapi.TYPE_NUMBER, required=True),
        ],
        tags=['app测评']
    )
    @action(methods=['get'], detail=False, url_path='coach/report/list')
    def coach_report_list(self, request, *args, **kwargs):

        queryset = self.get_queryset().filter(public_attr__user_id=request.query_params.get('coachee_user_id', 0)
                                              ).order_by('-updated_at')

        # 分页
        paginator = StandardResultsSetPagination()
        page_list = paginator.paginate_queryset(queryset, self.request)
        serializer = self.serializer_class(page_list, many=True, context={'queryset': queryset})
        response = paginator.get_paginated_response(serializer.data)
        return success_response(response, request=request)

    @swagger_auto_schema(
        operation_id='LBI测评报告修改信息',
        operation_summary='LBI测评报告修改信息',
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['evaluation_report_id'],
            properties={
                'evaluation_report_id': openapi.Schema(type=openapi.TYPE_NUMBER, description='测评报告id'),
                'self_considence': openapi.Schema(type=openapi.TYPE_NUMBER, description='报告中哪些内容和你的自我认知是一致的？'),
                'outstanding': openapi.Schema(type=openapi.TYPE_NUMBER, description='报告中哪些内容令你感到出乎意料？'),
                'discuss': openapi.Schema(type=openapi.TYPE_NUMBER, description='阅读完报告，你最想和教练讨论的是什么？'),
            }
        ),
        tags=['app测评']
    )
    @action(methods=['post'], detail=False, url_path='coachee/reply')
    def coach_report_list(self, request, *args, **kwargs):
        try:
            evaluation_report_id = request.data.get('evaluation_report_id')
            self_considence = request.data.get('self_considence')
            outstanding = request.data.get('outstanding')
            discuss = request.data.get('discuss')
            evaluation_report = EvaluationReport.objects.filter(
                deleted=False,
                id=evaluation_report_id,
                evaluation__code=LBI_EVALUATION,
                lbi_analysis__isnull=False
            ).first()
            if not evaluation_report:
                return parameter_error_response()
            lbi_analysis = evaluation_report.lbi_analysis

            if self_considence:
                lbi_analysis['self_considence'] = self_considence
            if outstanding:
                lbi_analysis['outstanding'] = outstanding
            if discuss:
                lbi_analysis['discuss'] = discuss
            evaluation_report.lbi_analysis = lbi_analysis
            evaluation_report.save()

            # 问题更新后，刷新pdf文件
            pdf_type = None
            if evaluation_report.get_report_type == EvaluationReportTypeEnum.lbi_personal_report.value:
                pdf_type = PdfReportTypeEnum.lbi_personal_report.value
            elif evaluation_report.get_report_type == EvaluationReportTypeEnum.lbi_self_evaluation_report.value:
                pdf_type = PdfReportTypeEnum.lbi_self_evaluation_report.value

            if pdf_type:
                task.get_user_pdf_url.delay(
                    evaluation_report_id,
                    f'{evaluation_report.public_attr.user.cover_name}'
                    f'_{evaluation_report.evaluation.name}_{evaluation_report_id}.pdf',
                    pdf_type=pdf_type
                )
        except Exception as e:
            return parameter_error_response(str(e))

        return success_response(data=lbi_analysis, request=request)
