import pendulum
from django.conf import settings
import redis

from utils.feishu_robot import push_wx_error_message
from utils.message.email import message_send_email_base
from utils import excel_pubilc, aliyun, randomPassword
from utils.message.lark_message import LarkMessageCenter
from wisdom_v2.common import project_member_public, interview_public, tag_public
from wisdom_v2.enum.message_type_enum import LarkMessageTypeEnum
from wisdom_v2.enum.project_interview_enum import InterviewRecordTypeEnum
from wisdom_v2.enum.service_content_enum import CoachTypeEnum, NoticeChannelTypeEnum, NoticeTemplateTypeEnum, TagObjectTypeEnum
from wisdom_v2.models import Project, ProjectInterviewRecord, Habit, ActionPlan, \
    ProjectMember, User, UserBackend, UserNoticeRecord
from wisdom_v2.models_file import ProjectTagGroup, TagObject, ProjectTagConfig
from wisdom_v2.views import constant
from django.db import transaction
from django.db.models import Sum

data_redis = redis.Redis.from_url(settings.DATA_REDIS)

# 获取指定时间结束的项目列表。
def send_projects_by_end_date_remind(end_date):
    """
    根据指定的结束日期，查询所有对应日期结束且未删除的项目，并通过飞书消息中心发送提醒。

    :param end_date: 指定的项目结束日期
    :return: 匹配的项目列表
    """

    project_list = Project.objects.filter(end_time=end_date, deleted=False).values_list(
        'name', 'company__short', 'company__name', 'start_time', 'end_time'
    )

    message_list = []
    for item in project_list:
        project_name, company_short, company_name, start_time, end_time = item
        company_real_name = company_short if company_short else company_name
        data = {
            'name': f'{company_real_name}-{project_name}',
            'date': f'{start_time.strftime("%Y.%m.%d")}-{end_time.strftime("%Y.%m.%d")}',
        }
        message_list.append(data)
    if message_list:
        LarkMessageCenter().send_business_message(
            message_list,
            LarkMessageTypeEnum.project_end_date_check.value,
            project_id=None,
        )
    return project_list


def get_project_coach_report_to_xlsx(project):
    """
    将指定项目的成长目标和辅导访谈信息导出为 Excel 文件，并上传至阿里云。
    :param project: 指定的项目实例
    :return: 上传文件的阿里云链接
    """

    project_members = ProjectMember.objects.filter(project=project, deleted=False).all()

    company_real_name = project.company.real_name

    data = []
    for project_member in project_members:

        growth_goals = project_member_public.get_project_member_coach_growth_goals(project_member)
        project_interview = project_member_public.get_project_member_interview(project_member)
        growth_goals_data = ['教练目标']

        for growth_goal_item in growth_goals:

            # 获取衡量指标-数据拼接
            change = growth_goal_item.growth_goals_change.filter(
                deleted=False, content__isnull=False).values_list('content', flat=True)
            if change:
                change = '\n    - '.join(change)
                growth_goals_data.append([
                    f'{growth_goal_item.content}\n    - {change}'
                ])
            else:
                growth_goals_data.append([
                    f'{growth_goal_item.content}'
                ])

        project_interview_title = ['辅导次数', '辅导日期', '辅导摘要', '教练反馈']
        if project.is_action_plan_to_report:
            project_interview_title.append('客户反思')
        else:
            project_interview_title.append('')

        project_interview_data = [project_interview_title]

        for _, interview in enumerate(project_interview):
            topic = f'·辅导议题：\n    -{interview.topic}' if interview.topic else None
            customer_data = None
            observation = '暂无'
            capacity = None
            if interview.record_type == InterviewRecordTypeEnum.question_and_answer.value:
                # 查询辅导报告
                record = ProjectInterviewRecord.objects.filter(
                    interview=interview, deleted=False).first()
                if record:
                    # 根据报告更新信息
                    observation = f'·{record.observation}' if record.observation else "暂无"
                    if record.leader_capacity:
                        capacity = f'·本次教练辅导是为了提升以下能力：\n    -{record.leader_capacity.title}'
            elif interview.record_type == InterviewRecordTypeEnum.questionnaire.value:
                # 议题
                topic_answer = interview_public.get_interview_to_question_answer(interview, 1)
                if topic_answer:
                    topic = f'·辅导议题：\n    -{topic_answer}'

                # 观察
                observation_answer = interview_public.get_interview_to_question_answer(interview, 2)
                if observation_answer:
                    observation = observation_answer

                # 领导力
                capacity_answer = interview_public.get_interview_to_question_answer(interview, 3)
                if capacity_answer:
                    capacity = f'·本次教练辅导是为了提升以下能力：\n    -{capacity_answer}'

            # 是否写入行动计划和习惯养成
            if project.is_action_plan_to_report:
                customer_data = []

                # 写入客户行动计划
                raw_coachee_action_plan = ActionPlan.objects.filter(
                    interview=interview, creator_role=constant.ROLE_COACHEE, deleted=False)
                for coachee_action_plan_item in raw_coachee_action_plan:
                    coachee_action_plan_content = coachee_action_plan_item.content
                    customer_data.append(f'·{coachee_action_plan_content}')

                # 写入教练行动计划
                raw_coach_action_plan = ActionPlan.objects.filter(
                    interview=interview, creator_role=constant.ROLE_COACH, deleted=False)
                for coach_action_plan_item in raw_coach_action_plan:
                    coach_action_plan_content = coach_action_plan_item.content
                    customer_data.append(f'·{coach_action_plan_content}')

                # 写入客户习惯养成
                raw_coachee_habit = Habit.objects.filter(
                    creator_role=constant.ROLE_COACHEE, interview=interview, deleted=False).all()
                for coachee_habit_item in raw_coachee_habit:
                    coachee_habit_content = f'·当{coachee_habit_item.when}会停止{coachee_habit_item.stop}转变为{coachee_habit_item.change}'
                    customer_data.append(coachee_habit_content)

                # 写入教练习惯养成
                raw_coach_action_plan = Habit.objects.filter(
                    creator_role=constant.ROLE_COACH, interview=interview, deleted=False).all()
                for coach_habit_item in raw_coach_action_plan:
                    coach_habit_content = f'·当{coach_habit_item.when}会停止{coach_habit_item.stop}转变为{coach_habit_item.change}'
                    customer_data.append(coach_habit_content)

            if customer_data is None:
                customer_reflection = ''
            else:
                customer_reflection = '\n'.join(customer_data) if customer_data else "暂无"

            if not topic and not capacity:
                abstract = '暂无'
            elif topic and capacity:
                abstract = f'{topic}\n{capacity}'
            else:
                abstract = topic if topic else capacity

            # 序列化的添加到总数据
            project_interview_data.append([
                _ + 1,
                f'{interview.public_attr.start_time.strftime("%Y.%m.%d %H:%M")}-{interview.public_attr.end_time.strftime("%H:%M")}',
                abstract,
                observation,
                customer_reflection
            ])

        data.append({
            'user_name': project_member.user.true_name,
            'growth_goals_data': growth_goals_data,
            'project_interview_data': project_interview_data,
        })

    #  文件名
    name = f'admin/project_coach_report/{pendulum.now().to_date_string()}/{randomPassword()}/{company_real_name}_{project.name}_教练报告.xlsx'

    # 获取文件数据
    file = excel_pubilc.save_project_coach_report_excel(data)

    # 上传阿里云
    aliyun.AliYun('cwcoach').send_file(name, file.getvalue())

    return f'{settings.ALIYUN_SDN_BASE_URL}/{name}'


def update_project_member_tag(project_member_id, tag_ids_list):
    """
    更新或创建项目成员的标签组，并根据提供的标签ID列表更新标签配置。

    :param project_member_id: int, 项目成员的ID。
    :param tag_ids_list: list, 包含要添加至项目成员的标签ID列表。

    :return: None, 函数执行后不返回任何值，但会更新数据库中的相关记录。
    """

    # 查找指定ID的ProjectMember实例，确保该实例未被删除
    project_member = ProjectMember.objects.filter(id=project_member_id, deleted=False).first()
    if not project_member:
        # 如果找不到相应的ProjectMember，则直接返回
        return

    # 查找与project_member_id关联且未被删除的第一个ProjectTagGroup实例
    project_tag_group = ProjectTagGroup.objects.filter(project_member_id=project_member_id, deleted=False).first()

    # 如果找到了ProjectTagGroup实例
    if project_tag_group:
        # 获取ProjectTagGroup的详细信息，不包括匹配标签
        project_tag_data = tag_public.get_project_tag_group_info(project_tag_group.id, is_matching_tag=False)
        # 从project_tag_data中获取已选择的标签ID列表
        selected_tag_ids = project_tag_data.get('selected_tag_ids')
    else:
        # 如果没有找到ProjectTagGroup，创建一个新的实例
        project_tag_group = ProjectTagGroup.objects.create(
            project_member_id=project_member_id, project_id=project_member.project_id,
            title=f'{project_member.user.true_name}需求', deleted=False)
        # 初始化已选择的标签ID列表为空
        selected_tag_ids = []

    # 创建用于存储新ProjectTagConfig实例的列表
    create_project_tag_config_list = []

    # 遍历传入的标签ID列表
    for tag_id in tag_ids_list:
        # 如果当前标签ID不在已选择的标签ID列表中
        if str(tag_id) not in selected_tag_ids:
            # 创建一个新的TagObject实例
            tag_object = TagObject.objects.create(
                object_type=TagObjectTypeEnum.project.value, object_id=project_member.project_id, tag_id=tag_id)
            # 将新创建的ProjectTagConfig实例添加到列表中
            create_project_tag_config_list.append(
                ProjectTagConfig(
                    tag_object_id=tag_object.id,
                    project_tag_group_id=project_tag_group.id
                )
            )

    # 如果有需要创建的新标签配置
    if create_project_tag_config_list:
        # 批量创建所有的ProjectTagConfig实例
        ProjectTagConfig.objects.bulk_create(create_project_tag_config_list)

    # 获取与ProjectTagGroup相关的教练内容
    coach_content = tag_public.get_coach_content(project_tag_group.id)
    # 更新ProjectTagGroup的coach_content字段
    project_tag_group.coach_content = coach_content
    # 保存更改
    project_tag_group.save()

    # 返回，此处可以返回一个状态码或消息，表示操作成功或失败
    return


def send_customer_info_collection_email(userbackend_id, task_key, sender_id=None):
    # 创建发送通知记录
    userbackend = UserBackend.objects.filter(id=userbackend_id, deleted=False).first()
    project = userbackend.project
    with transaction.atomic():
        obj = UserNoticeRecord.objects.create(
            project=project,
            user_id=userbackend.user_id,
            type=NoticeTemplateTypeEnum.customer_infomation_collection,
            channel=NoticeChannelTypeEnum.email,
            content={
                "manager_user_id": userbackend.user_id,
                "project_id": userbackend.project_id,
            },
        )
    page_url = f'{settings.SITE_URL.replace("www", "admin")}#/customer_info_collection?id={obj.uuid}'
    params = {
        'company_name': project.company.real_name,
        'project_name': project.name,
        'page_url': page_url,
    }
    user_backend = UserBackend.objects.filter(project=project, deleted=False, role__name='客户顾问').first()
    copy_email = [user_backend.user.email] if user_backend else None
    user = userbackend.user
    err = []
    state = message_send_email_base('customer_info_collection', params, [user.email], copy_email=copy_email,
                                   project_id=project.id, sender_id=sender_id, receiver_ids=user.id)

    if not state:
        push_wx_error_message(
            name=f'发送用户通知',
            level='warning', content={
                'error': 'customer_info_collection_notice', 'data': params,
                'details': f'发送失败，收件人：{user.email}，抄送人：{user_backend.user.email}',
            })
        err.append(user.true_name)

    task_id = data_redis.get(task_key)
    if not task_id:
        push_wx_error_message(
            name=f'发送用户通知',
            level='warning', content={
                'error': f'{task_key}:异步任务id丢失，请排查redis是否出现故障',
                'details': f'send_customer_info_collection_email发送失败',
                'data': params,
            })
        return False, '异步任务id丢失'
    if err:
        error = f'给{"、".join(err)}的邮件发送失败'
        data_redis.set(task_id.decode(), f'False&{error}', ex=3600)
        return False, error
    else:
        data_redis.set(task_id.decode(), f'True&发送成功', ex=3600)
        return True, '发送成功'
    

def send_increase_interview_hour_notice(project_member, sender_id=None):
    """
    发送辅导小时数增加通知
    :param project_member: 项目成员对象
    :param sender_id: 发送人id
    """
    project = project_member.project
    # 获取用户总辅导小时数，从服务配置获取
    all_times = 0
    project_bundle = project_member.project_bundle.filter(deleted=False).first()
    if project_bundle:
        online = project_bundle.one_to_one_coach.filter(type=CoachTypeEnum.online.value, deleted=False)
        if online.exists():
            all_times = online.aggregate(used_times=Sum('online_time'))
            all_times = all_times.get('used_times', 0) if all_times.get('used_times', 0) else 0
    
    
    # 获取用户在项目中的教练名字
    coach_name = project_member.coach_name
    with transaction.atomic():
        obj = UserNoticeRecord.objects.create(
            project=project,
            user_id=project_member.user_id,
            type=NoticeTemplateTypeEnum.increase_interview_hour.value,
            channel=NoticeChannelTypeEnum.email,
            content={
                "manager_user_id": project_member.user_id,
                "project_id": project_member.project_id,
                "all_times": all_times,
                "coach_name": coach_name,
            },
        )
    params = {
        'all_times': all_times,
        'coach_name': coach_name,
    }
    user = project_member.user
    message_send_email_base('increase_interview_hour', params, [user.email],
                                   project_id=project.id, sender_id=sender_id, receiver_ids=user.id)
