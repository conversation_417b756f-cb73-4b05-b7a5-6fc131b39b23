import pendulum
from django.conf import settings

content_type_to_message = {
    'add_user': '小程序授权提醒',
    'growth_goal_reminder': '目标设定提醒',
    'coach_add_interview': '辅导预约提醒',
    'interview_day': '辅导开始提醒',
    'formal_interview_30_minute': '三十分钟辅导开始提醒',
    'undone_interview': '辅导记录填写提醒',
    'next_interview': '下次辅导预约提醒',
    'coachee_record': '辅导记录查看提醒',
    'coach_task': '教练任务报告填写提醒',
    'evaluation_remind': '测评报告查看提醒',
    'manage_evaluation_report': '项目报告查看提醒',
    'growth_goal_change': '成长目标修改提醒',
    'change_observation_report_remind': '改变观察总结报告生成提醒',
    'stakeholder_coach_task_report_remind': '查看利益相关者调研报告',
    'interview_confirmed': '您有一条辅导预约待确认',
    'interview_cancel': '您有一条辅导预约被取消',
    'customer_coachee_record': '您有一条客户反馈待查看',
    'coachee_change_interview_time': '辅导时间已调整',
    'interview_record_write_remind': '您有一条辅导记录待填写',
    'interview_30_minute': '您有一次辅导即将开始',
    'add_personal_apply_user': '小程序授权提醒',
    'add_personal_apply_student_user': '教练入驻小程序授权提醒',
    'coach_add_materials': '教练补充信息提醒',
    'coach_update_resume': '教练简历填写',
    'platform_order_receiving': '平台允许接单提醒',
    'coach_agree_with_interview': '教练同意辅导提醒',
    'send_coach_offer': '群智企业项目邀请',
    'coach_chemical_interview_schedule': '设置化学面谈可预约时间',
    'coachee_add_chemical_interview': '进入化学面谈预约详情',
    'chemical_interview_before_30': '辅导开始提醒',
    'chemical_interview_later_30': '化学面谈报告填写提醒',
    'chemical_interview_rejected': '化学面谈匹配失败提醒',
    'chemical_interview_pass': '化学面谈匹配成功提醒',
    'chemical_interview_all_unselected': '化学面谈结束提醒',
    'stakeholder_interview_schedule': '设置利益相关者可预约时间',
    'stakeholder_interview_details': '新增一个利益相关者访谈',
    'stakeholder_interview_start_notice_24h': '访谈开始提醒',
    'stakeholder_interview_start_notice_30m': '辅导开始提醒',
    'coach_stakeholder_interview_fill_notice': '利益相关者访谈记录提醒',
    'stakeholder_interview_record_fill_notice': '填写利益相关者访谈报告',
    'upload_project_docs_evaluation': '测评报告上传提醒',
    'hr_selected_coach_resume': 'hr选择简历通知',
    'coach_settlement_money_info': '提现已到账消息提醒',
    'coach_request_settlement_info': '提现申请发起消息提醒',
    'activity_invitation_coach': '公益教练活动邀请',
    'internship_to_personal_agree_notice': '个人教练申请审核已通过',
    'internship_to_personal_refuse_notice': '个人教练申请审核未通过',
    'internship_entrant_notice': '见习教练申请审核已通过',
    'to_c_order_refund_notice': '个人辅导订单退款通知',
    'to_c_order_pay_notice': '个人辅导订单支付通知',
    'company_interview_remind': '企业面试提醒',
}


# 企业微信通知消息拼接
def get_work_wechat_content(content_type, **kwargs):
    text_content = None
    miniapp_content = None
    
    if content_type == 'send_coach_offer':
        # text 文本消息
        text_content = {
            'text': {
                'content': f"群智企业教练邀请你加入{kwargs.get('company_name')}的{kwargs.get('project_name')}，"
                           f"本邀请将于{kwargs.get('confirm_time')}失效，请点击下方小程序链接确认是否加入。"}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f"pages_coach/project_invitation/project_invitation?offer_id={kwargs.get('offer_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
            }
        }
    elif content_type == 'chemical_interview_rejected':
        # text 文本消息
        text_content = {
            'text': {
                'content': f"很遗憾您在{kwargs.get('project_name')}项目中与{kwargs.get('coachee_name')}进行化学面谈后匹配失败。"
                           f"及时复盘本次面谈过程，就能为下次的成功沉淀经验哦。"
            }
        }
    elif content_type == 'chemical_interview_pass':
        # text 文本消息
        text_content = {
            'text': {
                'content': f"恭喜您在{kwargs.get('project_name')}中与{kwargs.get('coachee_name')}进行化学面谈后匹配成功，"
                           f"请为后续的教练工作做好准备吧！"
            }
        }
    elif content_type == 'coach_chemical_interview_schedule':
        text_content = {
            'text': {
                'content': f"{kwargs.get('project_name')}化学面谈即将开始。请您于{kwargs.get('chemical_interview_time')}"
                           f"前完成{kwargs.get('chemical_interview_date')}可预约时间的设置，方便客户在此期间完成化学面谈。"}
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': "pages_coach/calendar/add_schedule?refer=2",
                'title': content_type_to_message.get(content_type),
            }
        }
    elif content_type == 'chemical_interview_later_30':
        text_content = {
            'text': {
                'content': f"{kwargs.get('project_name')}{kwargs.get('coachee_name')}的化学面谈已结束，"
                           f"请及时填写化学面谈总结报告"}
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f"pages_interview/record/record_group?interview_id={kwargs.get('interview_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'chemical_interview_before_30':
        text_content = {
            'text': {
                'content': f"{kwargs.get('project_name')}{kwargs.get('coachee_name')}的化学面谈，将在30分钟内开始，"
                           f"点击下方小程序链接查看客户详请，做好面谈准备"}
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f"pages_interview/interview/detail?id={kwargs.get('interview_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'coachee_add_chemical_interview':
        text_content = {
            'text': {
                'content': f"{kwargs.get('project_name')}的客户{kwargs.get('coachee_name')}预约您进行化学面谈，"
                           f"点击下方小程序链接了解详细信息"}
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f"pages_interview/interview/detail?id={kwargs.get('interview_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'add_personal_apply_user':
        # text 文本消息
        text_content = {
            'text': {
                'content': '您好，欢迎进入平台入驻签约流程。请点击下方小程序授权提醒，完成授权，开通平台教练账号。'}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': 'pages_coach/signing_agreement/signing_landing?refer=2',
                'title': content_type_to_message.get(content_type),
            }
        }

    elif content_type == "add_personal_apply_student_user":
        # text 文本消息
        text_content = {
            'text': {
                'content': '您好，欢迎进入平台入驻签约流程。请点击下方小程序授权提醒，完成授权，开通平台教练账号。'}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': 'pages_coach/signing_agreement/internship_entrant?refer=2',
                'title': content_type_to_message.get(content_type),
            }
        }

    elif content_type == 'growth_goal_reminder':
        # text 文本消息
        text_content = {
            'text': {
                'content': '已完成初次辅导，请尽快和客户沟通成长目标，点击下方小程序链接可直接填写'}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': 'pages_customer/customer/customer_details?user_id={}&current=2&growth_current=0&refer=2'.format(
                    kwargs.get('coachee_id')),
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'coach_add_interview':
        # text 文本消息
        text_content = {
            'text': {
                'content': '来自{}的客户{}预约了{}的辅导，点击下方小程序链接可查看详情'.format(
                    kwargs.get('company'),
                    kwargs.get('coachee_name'),
                    kwargs.get('date'))}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': 'pages/interview/interview?refer=2',
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'coachee_record':
        # text 文本消息
        text_content = {
            'text': {
                'content': '来自{}的客户{}已填写{}的辅导记录，点击下方小程序链接查看吧'.format(
                    kwargs.get('company'),
                    kwargs.get('coachee_name'),
                    kwargs.get('date'))}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': 'pages_interview/interview/detail?id={}&refer=2'.format(kwargs.get('interview_id')),
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'undone_interview':
        # text 文本消息
        text_content = {
            'text': {
                'content': '您和{}客户{}的辅导记录还未填写，请尽快完成'.format(
                    kwargs.get('company'),
                    kwargs.get('coachee_name'))}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': 'pages_interview/record/record_group?interview_id={}&refer=2'.format(kwargs.get('interview_id')),
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'next_interview':
        # text 文本消息
        text_content = {
            'text': {
                'content': '您{}的客户{}还未预约下次辅导，点击下方小程序链接，'
                           '设置您最近两周的可预约时间吧'.format(
                    kwargs.get('company'),
                    kwargs.get('coachee_name'))}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': 'pages_coach/calendar/calendar?refer=2',
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'formal_interview_30_minute':
        if kwargs.get('interview_id'):  # 上一次辅导的id
            # text 文本消息
            text_content = {
                'text': {
                    'content': '{}客户{}的教练辅导将在30分钟后开始'
                               '，点击下方小程序链接查看历史约谈记录，为本次约谈做好准备吧'.format(
                            kwargs.get('company'),
                            kwargs.get('coachee_name'))}
            }
            # miniprogram_notice 小程序通知消息
            miniapp_content = {
                'miniprogram_notice': {
                    'appid': settings.APP_ID,
                    'page': 'pages_interview/interview/detail?id={}&refer=2'.format(kwargs.get('interview_id')),
                    'title': content_type_to_message.get(content_type),
                    "description": pendulum.now().format('M月DD日 HH:mm'),
                    "emphasis_first_item": True,
                    "content_item": kwargs.get('content_item')
                }
            }
        else:
            text_content = {
                'text': {
                    'content': '{}客户{}的教练辅导将在30分钟后开始'.format(
                            kwargs.get('company'),
                            kwargs.get('coachee_name'))}
            }

    elif content_type == 'interview_day':
        # text 文本消息
        text_content = {
            'text': {
                'content': '您今天{}-{}与{}的客户{}有一次教练辅导，请准时参加'.format(
                            kwargs.get('start_time'),
                            kwargs.get('end_time'),
                            kwargs.get('company'),
                            kwargs.get('coachee_name'))}
        }

    elif content_type == 'coach_task':
        # text 文本消息
        text_content = {
            'text': {
                'content': '{}的客户{}已完成{}辅导，请及时填写{}报告'.format(
                            kwargs.get('company'),
                            kwargs.get('coachee_name'),
                            kwargs.get('task_name'),
                            kwargs.get('task_name'),
                )}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': 'pages_customer/coach_task/coach_task?coach_task_id={}&refer=2'.format(
                    kwargs.get('task_id')),
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'evaluation_remind':
        # text 文本消息
        text_content = {
            'text': {
                'content': f'您{kwargs.get("company")}的客户{kwargs.get("coachee_name")}LBI测评报告已生成，点击下方小程序链接，查看详情吧'}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f'pages_action/evaluation_report/evaluation_report_lbi?id={kwargs.get("report_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'add_msg_template':
        text_content = {}
        miniapp_content = {

                "external_userid": kwargs.get('external_userid_list'),
                "sender": kwargs.get('sender'),
                "text": {
                    "content": kwargs.get('text')
                },
                "miniprogram": {
                    "title": kwargs.get('title'),
                    "pic_media_id": kwargs.get('media_id'),
                    "appid": settings.APP_ID,
                    "page": kwargs.get('page')
                }

        }

    elif content_type == 'add_msg_template_content':
        text_content = {}
        miniapp_content = {
                "external_userid": kwargs.get('external_userid_list'),
                "sender": kwargs.get('sender'),
                "text": {
                    "content": kwargs.get('text')
                }

        }
    elif content_type == 'manage_evaluation_report':
        # {  # 项目名称#}的{#测评名称#}的项目报告已生成，点击下方小程序链接查看详情吧
        text_content = {
            'text': {
                'content': f'{kwargs.get("project_name")}的{kwargs.get("report_name")}已生成，点击下方小程序链接，查看详情吧'}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f'/pages_action/evaluation_report/evaluation_report_coach?id={kwargs.get("report_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'growth_goal_change':
        # 你的客户{  # 客户名称#}补充修改了成长目标，快去查看更新后的成长目标吧
        text_content = {
            'text': {
                'content': f'你的客户{kwargs.get("user_name")}补充修改了成长目标，快去查看更新后的成长目标吧！'}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f'/pages_customer/growth_goals/index/?growth_goals_id={kwargs.get("growth_goals_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'change_observation_report_remind':
        # 您{#企业名称#}的客户{#客户名称#}改变观察总结已生成，点击下方小程序链接，查看详情吧
        text_content = {
            'text': {
                'content': f'您{kwargs.get("company_name")}的客户{kwargs.get("user_name")}改变观察总结已生成，点击下方小程序链接，查看详情吧'}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f'/pages_change/change_report/change_report?report_id={kwargs.get("report_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'stakeholder_coach_task_report_remind':
        # 您{#企业名称#}的客户{#客户名称#}利益相关者调研报告已生成，点击下方小程序链接，查看详情吧
        text_content = {
            'text': {
                'content': f'您{kwargs.get("company_name")}的客户{kwargs.get("user_name")}利益相关者调研报告已生成，点击下方小程序链接，查看详情吧'}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f'pages_customer/coach_task/coach_task_stakeholder_report?report_id={kwargs.get("report_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'interview_confirmed':
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f"/pages_interview/interview/detail?id={kwargs.get('interview_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'interview_cancel':
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f"/pages_interview/interview/detail?id={kwargs.get('interview_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'customer_coachee_record':
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f"/pages_interview/interview/detail?id={kwargs.get('interview_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'coachee_change_interview_time':
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f"/pages_interview/interview/detail?id={kwargs.get('interview_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'interview_record_write_remind':
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f"/pages_interview/record/record_group?interview_id={kwargs.get('interview_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'interview_30_minute':
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                'page': f"/pages_interview/interview/detail?id={kwargs.get('interview_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
                "description": pendulum.now().format('M月DD日 HH:mm'),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'coach_add_materials':
        # text 文本消息
        text_content = {
            'text': {
                'content': """您好，欢迎进入签约流程的第三步。
为了给您结算费用和缴纳个税，请您点击下方连接，填写个人身份证号、银行卡号，并提供身份证正反面照片（身份证可以标注使用途径为“群智合作缴纳个税”，请注意不要遮挡文字和照片）
https://sx683824si.feishu.cn/share/base/form/shrcnLni8gUPSNUb4IXjq7j6UDf"""}
        }
    elif content_type == 'coach_update_resume':
        # text 文本消息
        text_content = {
            'text': {
                'content': """您好，请点击下方教练简历填写提醒，
完成平台平台入驻签约流程"""}
        }
        # miniprogram_notice 小程序通知消息
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_coach/signing_agreement/signing_landing?refer=2',
                'title': content_type_to_message.get(content_type),
            }
        }

    elif content_type == 'platform_order_receiving':
        text_content = {
            'text': {
                'content': """恭喜，已为您开启群智教练平台推荐功能！
为了让客户预约你时更加高效顺畅，请仔细了解以下内容：
1、点击“日程管理”菜单，在日程页面下方点击“添加按钮”，添加可预约的时间，这样客户就可以选择合适的辅导时间。
2、完成日程设置后，点击“首页 - 邀请客户”可以分享自己的教练海报，让更多的潜在客户看到你。
3、平台为每位新注册的客户设置了三折优惠券，该优惠券仅限客户在平台首次约谈时使用。如果你不希望参与客户三折体验教练的活动，请给********************发邮件联系群智支持团队。
"""}
        }
    elif content_type == 'coach_agree_with_interview':
        text_content = {
            'text': {
                'content': """很开心看到你确认了一条新的教练辅导预约。确认好以下事项可以让你们的约谈准备更充分：
1、在辅导详情页点击“进入视频辅导”，即可和客户通过平台视频通话完成辅导。请尽量保证在视频期间处于良好的网络环境中；
2、提醒客户阅读平台上关于教练辅导的介绍内容，了解什么是教练，通过教练可以获得什么；
3、在会议开始前提醒客户找到一个安静的房间，确认网络流畅，准备好可以记录自己思考的笔记本或纸笔。
如果你和客户的沟通遇到问题，请给********************发邮件联系群智支持团队。"""}
        }

    elif content_type == 'stakeholder_interview_schedule':
        text_content = {
            'text': {
                'content': f'{kwargs.get("project_name")}的利益相关者访谈即将开始。请您于{kwargs.get("end_time")}前完成{kwargs.get("interview_date")}期间可预约时间的设置，方便客户在此期间完成访谈。'
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_coach/calendar/add_schedule?refer=2',
                'title': content_type_to_message.get(content_type),
            }
        }

    elif content_type == 'stakeholder_interview_details':
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_interview/interview/detail?id={kwargs.get("interview_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'stakeholder_interview_start_notice_24h':
        text_content = {
            'text': {
                'content': f"{kwargs.get('project_name')}{kwargs.get('coachee_name')}的利益相关者访谈，将在明天{kwargs.get('interview_time')}开始，点击下方小程序链接查看客户详情，做好访谈准备"
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_interview/interview/detail?id={kwargs.get("interview_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'stakeholder_interview_start_notice_30m':
        text_content = {
            'text': {
                'content': f"{kwargs.get('project_name')}{kwargs.get('coachee_name')}的利益相关者访谈，还有30分钟就要开始了，点击下方小程序链接查看客户详情，做好访谈准备"
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_interview/interview/detail?id={kwargs.get("interview_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'coach_stakeholder_interview_fill_notice':
        text_content = {
            'text': {
                'content': f"与{kwargs.get('project_name')}{kwargs.get('coachee_name')}的利益相关者{kwargs.get('stakeholder_name')}访谈已结束，记录关键信息，为访谈报告汇总提供信息"
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_interview/record/record_group?interview_id={kwargs.get("interview_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'stakeholder_interview_record_fill_notice':
        text_content = {
            'text': {
                'content': f"{kwargs.get('project_name')}{kwargs.get('coachee_name')}的利益相关者访谈已全部结束，请及时填写利益相关者总结报告，信息将呈现给企业查看，请认真填写"
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_customer/coach_task/coach_task?coach_task_id={kwargs.get("coach_task_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'upload_project_docs_evaluation':
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_customer/customer/report?customer_id={kwargs.get("coachee_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "content_item": kwargs.get('content_item')
            }
        }
    elif content_type == 'hr_selected_coach_resume':
        text_content = {
            'text': {
                'content': f"企业管理员已确认{kwargs.get('project_name')}的教练名单，请进入运营后台-项目列表-教练查看"
            }
        }
    elif content_type == 'coach_settlement_money_info':
        text_content = {
            'text': {
                'content': f"您提交的提现已经到账，到账金额{kwargs.get('money')}元"
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_income/platform_income/platform_income?current=1&refer=2',
                'title': content_type_to_message.get(content_type),
            }
        }
    elif content_type == 'coach_request_settlement_info':
        text_content = {
            'text': {
                'content': f"您已发起订单提现申请，预计收益{kwargs.get('money')}元，申请正在处理中，请耐心等待"
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_income/platform_income/platform_income?refer=2',
                'title': content_type_to_message.get(content_type),
            }
        }

    elif content_type == 'activity_invitation_coach':
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_coach/coach_invitation/coach_invitation?activity_coach_id={kwargs.get("activity_coach_id")}&refer=2',
                'title': content_type_to_message.get(content_type),
                "emphasis_first_item": True,
                "content_item": kwargs.get('content_item')
            }
        }

    elif content_type == 'internship_to_personal_agree_notice':
        text_content = {
            'text': {
                'content': f"个人教练申请审核结果通知：你已成功入驻成为平台个人教练！记得打开企微消息通知，及时确认平台客户预约哦~"
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages/index/index?refer=2',
                'title': content_type_to_message.get(content_type)
            }
        }

    elif content_type == 'internship_to_personal_refuse_notice':
        text_content = {
            'text': {
                'content': f"个人教练申请审核已被拒绝，{kwargs.get('rejection_reason')}"
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f'pages_coach/signing_agreement/internship_personal?refer=2',
                'title': content_type_to_message.get(content_type)
            }
        }

    elif content_type == 'internship_entrant_notice':
        text_content = {
            'text': {
                'content': f"见习教练申请审核结果通知：你的教练账号已经开通啦！快来生成你的专属教练海报，让更多的潜在客户看见你！"
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': 'pages/index/index?refer=2',
                'title': content_type_to_message.get(content_type)
            }
        }

    elif content_type == "to_c_order_refund_notice":
        text_content = {
            'text': {
                'content': f"您有一条个人辅导订单，客户已操作退款，退款金额：{kwargs.get('refund_money')}元。"
            }
        }

    elif content_type == "to_c_order_pay_notice":
        text_content = {
            'text': {
                'content': f"""您有一条个人辅导订单已生成，客户已支付金额：{kwargs.get('pay_money')}元。
正在等待客户预约辅导时间。
如果您需要开放更多可预约时间，可点击页面下方菜单的“日程管理”"""}
        }
    elif content_type == "chemical_interview_all_unselected":
        text_content = {
            'text': {
                'content': f"{kwargs.get('project_name')}项目中的客户已完成教练选择。本次项目无客户预约您的化学面谈，感谢您的参与。"
            }
        }
    elif content_type == "company_interview_remind":
        text_content = {
            'text': {
                'content': f"{kwargs.get('project_name')}项目企业面试提醒，请注意准时参加。"
            }
        }
        miniapp_content = {
            'miniprogram_notice': {
                'appid': settings.APP_ID,
                "description": pendulum.now().format('M月DD日 HH:mm'),
                'page': f"pages_coach/company_interview/company_interview?interview_id={kwargs.get('interview_id')}&refer=2",
                'title': content_type_to_message.get(content_type),
                "content_item": kwargs.get('content_item')
            }
        }


    if miniapp_content:
        title = miniapp_content.get('miniprogram_notice', {}).get('title')
        if title and len(str(title)) > 30:
            miniapp_content['miniprogram_notice']['title'] = title[:29] + '...'
        content_item = miniapp_content.get('miniprogram_notice', {}).get('content_item', [])
        for item in content_item:
            if item.get('value') and len(str(item.get('value'))) > 30:
                item['value'] = item.get('value')[:29] + '...'

    return text_content, miniapp_content
